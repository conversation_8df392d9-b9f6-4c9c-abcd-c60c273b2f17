#Thu Jul 31 16:53:44 MYT 2025
com.example.standardtemplate.app-main-46\:/drawable/application_icon.png=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\drawable_application_icon.png.flat
com.example.standardtemplate.app-main-46\:/drawable/dialog_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\drawable_dialog_background.xml.flat
com.example.standardtemplate.app-main-46\:/drawable/ic_back.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_back.xml.flat
com.example.standardtemplate.app-main-46\:/drawable/ic_close.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_close.xml.flat
com.example.standardtemplate.app-main-46\:/drawable/ic_launcher_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_background.xml.flat
com.example.standardtemplate.app-main-46\:/drawable/ic_launcher_foreground.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_foreground.xml.flat
com.example.standardtemplate.app-main-46\:/drawable/ic_noti_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_noti_background.xml.flat
com.example.standardtemplate.app-main-46\:/drawable/input_field_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\drawable_input_field_background.xml.flat
com.example.standardtemplate.app-main-46\:/layout/activity_accepted_ticket_details.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_accepted_ticket_details.xml.flat
com.example.standardtemplate.app-main-46\:/layout/activity_accepted_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_accepted_ticket_list.xml.flat
com.example.standardtemplate.app-main-46\:/layout/activity_login.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_login.xml.flat
com.example.standardtemplate.app-main-46\:/layout/activity_new_ticket_details.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_new_ticket_details.xml.flat
com.example.standardtemplate.app-main-46\:/layout/activity_new_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_new_ticket_list.xml.flat
com.example.standardtemplate.app-main-46\:/layout/activity_register.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_register.xml.flat
com.example.standardtemplate.app-main-46\:/layout/dialog_confirmation.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_dialog_confirmation.xml.flat
com.example.standardtemplate.app-main-46\:/layout/dialog_create.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_dialog_create.xml.flat
com.example.standardtemplate.app-main-46\:/layout/dialog_message.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_dialog_message.xml.flat
com.example.standardtemplate.app-main-46\:/layout/dialog_setting.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_dialog_setting.xml.flat
com.example.standardtemplate.app-main-46\:/layout/item_accepted_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_item_accepted_ticket_list.xml.flat
com.example.standardtemplate.app-main-46\:/layout/recycle_view_new_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_recycle_view_new_ticket_list.xml.flat
com.example.standardtemplate.app-main-46\:/layout/spinner_item.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_spinner_item.xml.flat
com.example.standardtemplate.app-main-46\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.standardtemplate.app-main-46\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.standardtemplate.app-main-46\:/mipmap-hdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher.webp.flat
com.example.standardtemplate.app-main-46\:/mipmap-hdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher_foreground.webp.flat
com.example.standardtemplate.app-main-46\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.standardtemplate.app-main-46\:/mipmap-mdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher.webp.flat
com.example.standardtemplate.app-main-46\:/mipmap-mdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher_foreground.webp.flat
com.example.standardtemplate.app-main-46\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.standardtemplate.app-main-46\:/mipmap-xhdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.standardtemplate.app-main-46\:/mipmap-xhdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_ic_launcher_foreground.webp.flat
com.example.standardtemplate.app-main-46\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.standardtemplate.app-main-46\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.standardtemplate.app-main-46\:/mipmap-xxhdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat
com.example.standardtemplate.app-main-46\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.standardtemplate.app-main-46\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.standardtemplate.app-main-46\:/mipmap-xxxhdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat
com.example.standardtemplate.app-main-46\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.standardtemplate.app-main-46\:/xml/backup_rules.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\xml_backup_rules.xml.flat
com.example.standardtemplate.app-main-46\:/xml/data_extraction_rules.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\xml_data_extraction_rules.xml.flat
com.example.standardtemplate.app-main-46\:/xml/locales_config.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\xml_locales_config.xml.flat
com.example.standardtemplate.app-main-47\:/drawable/application_icon.png=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\drawable_application_icon.png.flat
com.example.standardtemplate.app-main-47\:/drawable/dialog_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\drawable_dialog_background.xml.flat
com.example.standardtemplate.app-main-47\:/drawable/ic_back.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_back.xml.flat
com.example.standardtemplate.app-main-47\:/drawable/ic_close.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_close.xml.flat
com.example.standardtemplate.app-main-47\:/drawable/ic_launcher_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_background.xml.flat
com.example.standardtemplate.app-main-47\:/drawable/ic_launcher_foreground.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_foreground.xml.flat
com.example.standardtemplate.app-main-47\:/drawable/ic_noti_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_noti_background.xml.flat
com.example.standardtemplate.app-main-47\:/drawable/input_field_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\drawable_input_field_background.xml.flat
com.example.standardtemplate.app-main-47\:/layout/activity_accepted_ticket_details.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_accepted_ticket_details.xml.flat
com.example.standardtemplate.app-main-47\:/layout/activity_accepted_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_accepted_ticket_list.xml.flat
com.example.standardtemplate.app-main-47\:/layout/activity_login.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_login.xml.flat
com.example.standardtemplate.app-main-47\:/layout/activity_new_ticket_details.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_new_ticket_details.xml.flat
com.example.standardtemplate.app-main-47\:/layout/activity_new_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_new_ticket_list.xml.flat
com.example.standardtemplate.app-main-47\:/layout/activity_register.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_register.xml.flat
com.example.standardtemplate.app-main-47\:/layout/dialog_confirmation.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_dialog_confirmation.xml.flat
com.example.standardtemplate.app-main-47\:/layout/dialog_create.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_dialog_create.xml.flat
com.example.standardtemplate.app-main-47\:/layout/dialog_message.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_dialog_message.xml.flat
com.example.standardtemplate.app-main-47\:/layout/dialog_setting.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_dialog_setting.xml.flat
com.example.standardtemplate.app-main-47\:/layout/item_accepted_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_item_accepted_ticket_list.xml.flat
com.example.standardtemplate.app-main-47\:/layout/recycle_view_new_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_recycle_view_new_ticket_list.xml.flat
com.example.standardtemplate.app-main-47\:/layout/spinner_item.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\layout_spinner_item.xml.flat
com.example.standardtemplate.app-main-47\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.standardtemplate.app-main-47\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.standardtemplate.app-main-47\:/mipmap-hdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher.webp.flat
com.example.standardtemplate.app-main-47\:/mipmap-hdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher_foreground.webp.flat
com.example.standardtemplate.app-main-47\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.standardtemplate.app-main-47\:/mipmap-mdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher.webp.flat
com.example.standardtemplate.app-main-47\:/mipmap-mdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher_foreground.webp.flat
com.example.standardtemplate.app-main-47\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.standardtemplate.app-main-47\:/mipmap-xhdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.standardtemplate.app-main-47\:/mipmap-xhdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_ic_launcher_foreground.webp.flat
com.example.standardtemplate.app-main-47\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.standardtemplate.app-main-47\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.standardtemplate.app-main-47\:/mipmap-xxhdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat
com.example.standardtemplate.app-main-47\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.standardtemplate.app-main-47\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.standardtemplate.app-main-47\:/mipmap-xxxhdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat
com.example.standardtemplate.app-main-47\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.standardtemplate.app-main-47\:/xml/backup_rules.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\xml_backup_rules.xml.flat
com.example.standardtemplate.app-main-47\:/xml/data_extraction_rules.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\xml_data_extraction_rules.xml.flat
com.example.standardtemplate.app-main-47\:/xml/locales_config.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\xml_locales_config.xml.flat
