<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Activities.NewTickets.NewTicketDetailsActivity"
    android:background="@color/white"
    android:padding="10dp"
    >

    <!-- Title -->
    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/newTicketTitle"
        android:textSize="22sp"
        android:textStyle="bold"
        android:textAlignment="center"
        android:textColor="@color/primaryColor"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="30dp"
        android:layout_marginBottom="10dp"
        android:padding="4dp"/>

    <!-- CardView for Ticket Details -->
    <androidx.cardview.widget.CardView
        android:id="@+id/ticketCardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@android:color/white"
        app:layout_constraintTop_toBottomOf="@id/txtTitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_margin="30dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Machine ID -->
            <TextView
                android:id="@+id/txtMachineId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/txtmachineId"
                android:textSize="18sp"
                android:textColor="@color/textColor"
                android:padding="4dp"/>

            <!-- Machine Status -->
            <TextView
                android:id="@+id/txtMachineStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/machStatus"
                android:textSize="16sp"
                android:textColor="@color/textColor"
                android:padding="4dp"/>

            <!-- Error Message -->
            <TextView
                android:id="@+id/txtErrorMsg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/txtErrorMsg"
                android:textSize="16sp"
                android:textColor="@color/textColor"
                android:padding="4dp"/>

            <!-- Divider -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@android:color/darker_gray"
                android:layout_marginVertical="8dp"/>

            <!-- Created At -->
            <TextView
                android:id="@+id/txtCreatedAt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/txtCreatedAt"
                android:textSize="14sp"
                android:textColor="@color/textColor"
                android:layout_marginTop="10dp"
                android:padding="4dp"/>

            <!-- Creator -->
            <TextView
                android:id="@+id/txtCreator"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/txtCreator"
                android:textSize="14sp"
                android:textColor="@color/textColor"
                android:layout_marginTop="3dp"
                android:padding="4dp"/>

            <!-- Status -->
            <TextView
                android:id="@+id/txtStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/txtStatus"
                android:textSize="14sp"
                android:textColor="@color/textColor"
                android:layout_marginTop="3dp"
                android:padding="4dp"/>

            <!-- Machine Downtime -->
            <TextView
                android:id="@+id/txtMachineDowntime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/txtMachDowntime"
                android:textSize="14sp"
                android:textColor="@color/textColor"
                android:layout_marginTop="3dp"
                android:padding="4dp"/>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Back Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnBack"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:layout_marginTop="24dp"
        android:text="@string/btnBack"
        android:textSize="16sp"
        android:textColor="@color/white"
        android:backgroundTint="@color/primaryDarkColor"
        app:cornerRadius="8dp"
        app:layout_constraintTop_toBottomOf="@id/ticketCardView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btnAccept"
        android:layout_marginLeft="10dp"
        android:padding="4dp"/>

    <!-- Accept Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnAccept"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:layout_marginTop="24dp"
        android:text="@string/btnAccept"
        android:textSize="16sp"
        android:textColor="@color/white"
        android:backgroundTint="@color/primaryColor"
        app:cornerRadius="8dp"
        app:layout_constraintTop_toBottomOf="@id/ticketCardView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btnBack"
        android:layout_marginRight="10dp"
        android:padding="4dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>