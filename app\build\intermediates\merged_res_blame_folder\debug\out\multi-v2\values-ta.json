{"logs": [{"outputFile": "com.example.standardtemplate.app-mergeDebugResources-43:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c2425dbf60aad0ac4ec1f17c3e6f842d\\transformed\\appcompat-1.7.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,435,537,652,741,852,973,1052,1128,1226,1326,1421,1515,1622,1722,1824,1918,2016,2114,2195,2303,2406,2505,2621,2724,2829,2986,12080", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "430,532,647,736,847,968,1047,1123,1221,1321,1416,1510,1617,1717,1819,1913,2011,2109,2190,2298,2401,2500,2616,2719,2824,2981,3083,12157"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d1a93affe383e101aa07878b40831cae\\transformed\\material-1.12.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,360,446,530,633,727,836,954,1038,1097,1161,1269,1337,1398,1506,1573,1659,1717,1801,1868,1922,2045,2107,2170,2224,2312,2440,2526,2618,2721,2813,2895,3027,3107,3188,3344,3433,3517,3574,3626,3692,3777,3865,3936,4016,4085,4162,4242,4310,4425,4524,4607,4699,4793,4867,4953,5047,5097,5180,5246,5331,5418,5481,5546,5609,5678,5786,5884,5982,6079,6140,6196,6282,6374,6457", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,87,85,83,102,93,108,117,83,58,63,107,67,60,107,66,85,57,83,66,53,122,61,62,53,87,127,85,91,102,91,81,131,79,80,155,88,83,56,51,65,84,87,70,79,68,76,79,67,114,98,82,91,93,73,85,93,49,82,65,84,86,62,64,62,68,107,97,97,96,60,55,85,91,82,81", "endOffsets": "267,355,441,525,628,722,831,949,1033,1092,1156,1264,1332,1393,1501,1568,1654,1712,1796,1863,1917,2040,2102,2165,2219,2307,2435,2521,2613,2716,2808,2890,3022,3102,3183,3339,3428,3512,3569,3621,3687,3772,3860,3931,4011,4080,4157,4237,4305,4420,4519,4602,4694,4788,4862,4948,5042,5092,5175,5241,5326,5413,5476,5541,5604,5673,5781,5879,5977,6074,6135,6191,6277,6369,6452,6534"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3088,3176,3262,3346,3449,4289,4398,4516,6836,6895,6959,7067,7135,7196,7304,7371,7457,7515,7599,7666,7720,7843,7905,7968,8022,8110,8238,8324,8416,8519,8611,8693,8825,8905,8986,9142,9231,9315,9372,9424,9490,9575,9663,9734,9814,9883,9960,10040,10108,10223,10322,10405,10497,10591,10665,10751,10845,10895,10978,11044,11129,11216,11279,11344,11407,11476,11584,11682,11780,11877,11938,11994,12162,12254,12337", "endLines": "5,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,131,132,133", "endColumns": "12,87,85,83,102,93,108,117,83,58,63,107,67,60,107,66,85,57,83,66,53,122,61,62,53,87,127,85,91,102,91,81,131,79,80,155,88,83,56,51,65,84,87,70,79,68,76,79,67,114,98,82,91,93,73,85,93,49,82,65,84,86,62,64,62,68,107,97,97,96,60,55,85,91,82,81", "endOffsets": "317,3171,3257,3341,3444,3538,4393,4511,4595,6890,6954,7062,7130,7191,7299,7366,7452,7510,7594,7661,7715,7838,7900,7963,8017,8105,8233,8319,8411,8514,8606,8688,8820,8900,8981,9137,9226,9310,9367,9419,9485,9570,9658,9729,9809,9878,9955,10035,10103,10218,10317,10400,10492,10586,10660,10746,10840,10890,10973,11039,11124,11211,11274,11339,11402,11471,11579,11677,11775,11872,11933,11989,12075,12249,12332,12414"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\436b23367b1f7618e21fb32754334ccb\\transformed\\play-services-basement-18.1.0\\res\\values-ta\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "156", "endOffsets": "351"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5584", "endColumns": "160", "endOffsets": "5740"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b5bcb9b632ef6bbd4d6721f62c9768f1\\transformed\\core-1.13.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "38,39,40,41,42,43,44,134", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3543,3639,3742,3841,3939,4046,4161,12419", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "3634,3737,3836,3934,4041,4156,4284,12515"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8ddbf4dfd0b92e33e56ede5751f3d034\\transformed\\play-services-base-18.0.1\\res\\values-ta\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,667,815,939,1048,1145,1321,1424,1573,1704,1854,2006,2064,2123", "endColumns": "100,147,122,101,147,123,108,96,175,102,148,130,149,151,57,58,76", "endOffsets": "293,441,564,666,814,938,1047,1144,1320,1423,1572,1703,1853,2005,2063,2122,2199"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4600,4705,4857,4984,5090,5242,5370,5483,5745,5925,6032,6185,6320,6474,6630,6692,6755", "endColumns": "104,151,126,105,151,127,112,100,179,106,152,134,153,155,61,62,80", "endOffsets": "4700,4852,4979,5085,5237,5365,5478,5579,5920,6027,6180,6315,6469,6625,6687,6750,6831"}}]}]}