[{"merged": "com.example.standardtemplate.app-merged_res-43:/mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "com.example.standardtemplate.app-main-45:/mipmap-xhdpi/ic_launcher_foreground.webp"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.standardtemplate.app-main-45:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "com.example.standardtemplate.app-main-45:/mipmap-xxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/drawable_application_icon.png.flat", "source": "com.example.standardtemplate.app-main-45:/drawable/application_icon.png"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.standardtemplate.app-main-45:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.standardtemplate.app-main-45:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/layout_dialog_confirmation.xml.flat", "source": "com.example.standardtemplate.app-main-45:/layout/dialog_confirmation.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/layout_activity_new_ticket_list.xml.flat", "source": "com.example.standardtemplate.app-main-45:/layout/activity_new_ticket_list.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/layout_activity_new_ticket_details.xml.flat", "source": "com.example.standardtemplate.app-main-45:/layout/activity_new_ticket_details.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/layout_dialog_create.xml.flat", "source": "com.example.standardtemplate.app-main-45:/layout/dialog_create.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "com.example.standardtemplate.app-main-45:/mipmap-mdpi/ic_launcher_foreground.webp"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/xml_locales_config.xml.flat", "source": "com.example.standardtemplate.app-main-45:/xml/locales_config.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/layout_activity_login.xml.flat", "source": "com.example.standardtemplate.app-main-45:/layout/activity_login.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/drawable_ic_launcher_background.xml.flat", "source": "com.example.standardtemplate.app-main-45:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/drawable_ic_back.xml.flat", "source": "com.example.standardtemplate.app-main-45:/drawable/ic_back.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/drawable_dialog_background.xml.flat", "source": "com.example.standardtemplate.app-main-45:/drawable/dialog_background.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.example.standardtemplate.app-main-45:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.example.standardtemplate.app-main-45:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/drawable_ic_launcher_foreground.xml.flat", "source": "com.example.standardtemplate.app-main-45:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/layout_spinner_item.xml.flat", "source": "com.example.standardtemplate.app-main-45:/layout/spinner_item.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.example.standardtemplate.app-main-45:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/layout_dialog_setting.xml.flat", "source": "com.example.standardtemplate.app-main-45:/layout/dialog_setting.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.example.standardtemplate.app-main-45:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "com.example.standardtemplate.app-main-45:/mipmap-xxxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/layout_activity_accepted_ticket_details.xml.flat", "source": "com.example.standardtemplate.app-main-45:/layout/activity_accepted_ticket_details.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/drawable_ic_close.xml.flat", "source": "com.example.standardtemplate.app-main-45:/drawable/ic_close.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.standardtemplate.app-main-45:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/drawable_input_field_background.xml.flat", "source": "com.example.standardtemplate.app-main-45:/drawable/input_field_background.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/xml_backup_rules.xml.flat", "source": "com.example.standardtemplate.app-main-45:/xml/backup_rules.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/layout_recycle_view_new_ticket_list.xml.flat", "source": "com.example.standardtemplate.app-main-45:/layout/recycle_view_new_ticket_list.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/drawable_ic_noti_background.xml.flat", "source": "com.example.standardtemplate.app-main-45:/drawable/ic_noti_background.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/layout_activity_accepted_ticket_list.xml.flat", "source": "com.example.standardtemplate.app-main-45:/layout/activity_accepted_ticket_list.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/layout_dialog_message.xml.flat", "source": "com.example.standardtemplate.app-main-45:/layout/dialog_message.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "com.example.standardtemplate.app-main-45:/mipmap-hdpi/ic_launcher_foreground.webp"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.example.standardtemplate.app-main-45:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/layout_item_accepted_ticket_list.xml.flat", "source": "com.example.standardtemplate.app-main-45:/layout/item_accepted_ticket_list.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/xml_data_extraction_rules.xml.flat", "source": "com.example.standardtemplate.app-main-45:/xml/data_extraction_rules.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.example.standardtemplate.app-main-45:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/layout_activity_register.xml.flat", "source": "com.example.standardtemplate.app-main-45:/layout/activity_register.xml"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.example.standardtemplate.app-main-45:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.example.standardtemplate.app-merged_res-43:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.standardtemplate.app-main-45:/mipmap-xxhdpi/ic_launcher.webp"}]