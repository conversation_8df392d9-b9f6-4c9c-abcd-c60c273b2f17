com.example.standardtemplate.app-profileinstaller-1.4.0-0 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0721fcdb69099d92c89c40e6f9aec8b0\transformed\profileinstaller-1.4.0\res
com.example.standardtemplate.app-sqlite-framework-2.3.0-1 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0729944f40d4642f73c513ac157afaad\transformed\sqlite-framework-2.3.0\res
com.example.standardtemplate.app-emoji2-1.3.0-2 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07d8c6998e6fc6040ca69b8e3163b713\transformed\emoji2-1.3.0\res
com.example.standardtemplate.app-firebase-common-20.3.2-3 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\res
com.example.standardtemplate.app-lifecycle-service-2.6.2-4 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fb7240d98e8387afb3f48174087e847\transformed\lifecycle-service-2.6.2\res
com.example.standardtemplate.app-savedstate-1.2.1-5 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1328923033da8ce6690f733d346fffff\transformed\savedstate-1.2.1\res
com.example.standardtemplate.app-lifecycle-runtime-2.6.2-6 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22ca26fa054d5d805bbd05cba7bc797f\transformed\lifecycle-runtime-2.6.2\res
com.example.standardtemplate.app-annotation-experimental-1.4.0-7 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e98dbab7e2063fd62c50e0a369c5177\transformed\annotation-experimental-1.4.0\res
com.example.standardtemplate.app-drawerlayout-1.1.1-8 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35f1c6224769944de111539ab4bfbf5a\transformed\drawerlayout-1.1.1\res
com.example.standardtemplate.app-core-runtime-2.2.0-9 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d4bfe6fe11cd2c4a8eb8da2b922a74a\transformed\core-runtime-2.2.0\res
com.example.standardtemplate.app-startup-runtime-1.1.1-10 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3df6cc11034f0f7e0731682ed2166121\transformed\startup-runtime-1.1.1\res
com.example.standardtemplate.app-constraintlayout-2.2.1-11 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\423fb7a69eb9ce260214c5dd968e5fb2\transformed\constraintlayout-2.2.1\res
com.example.standardtemplate.app-play-services-basement-18.1.0-12 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\436b23367b1f7618e21fb32754334ccb\transformed\play-services-basement-18.1.0\res
com.example.standardtemplate.app-firebase-messaging-23.1.2-13 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\res
com.example.standardtemplate.app-lifecycle-viewmodel-2.6.2-14 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\516bc6b98155bc4f738de1d591eb873a\transformed\lifecycle-viewmodel-2.6.2\res
com.example.standardtemplate.app-recyclerview-1.1.0-15 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c967e6935ddea2d32a85e4bd50b38c3\transformed\recyclerview-1.1.0\res
com.example.standardtemplate.app-appcompat-resources-1.7.0-16 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65d46d132c0a99e7d206258618d61cb0\transformed\appcompat-resources-1.7.0\res
com.example.standardtemplate.app-activity-1.10.1-17 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a15594bf2c2123cd4d893951be3e52c\transformed\activity-1.10.1\res
com.example.standardtemplate.app-viewpager2-1.0.0-18 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73099662db7bf98f4262269fb8a030e7\transformed\viewpager2-1.0.0\res
com.example.standardtemplate.app-work-runtime-ktx-2.9.0-19 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7add0eeee191a2631d6200ffb6df7f5e\transformed\work-runtime-ktx-2.9.0\res
com.example.standardtemplate.app-work-runtime-2.9.0-20 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\res
com.example.standardtemplate.app-transition-1.5.0-21 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ff105fdf3caa960c9b672cc625d5264\transformed\transition-1.5.0\res
com.example.standardtemplate.app-emoji2-views-helper-1.3.0-22 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\842ade66c423e2016e2b1d63fe6b9575\transformed\emoji2-views-helper-1.3.0\res
com.example.standardtemplate.app-core-viewtree-1.0.0-23 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86e1bc4cee6153ea8951ea43962ea823\transformed\core-viewtree-1.0.0\res
com.example.standardtemplate.app-lifecycle-livedata-core-2.6.2-24 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\888c42f7ab319b215b81caeebc4e849a\transformed\lifecycle-livedata-core-2.6.2\res
com.example.standardtemplate.app-lifecycle-process-2.6.2-25 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c983d50f1fe996c2f58120783389b9b\transformed\lifecycle-process-2.6.2\res
com.example.standardtemplate.app-play-services-base-18.0.1-26 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ddbf4dfd0b92e33e56ede5751f3d034\transformed\play-services-base-18.0.1\res
com.example.standardtemplate.app-coordinatorlayout-1.1.0-27 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cb6f6c13d90d9a02baba802ba1cd63\transformed\coordinatorlayout-1.1.0\res
com.example.standardtemplate.app-sqlite-2.3.0-28 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7b1b79dfcdbcbc3025946c780c9e3a6\transformed\sqlite-2.3.0\res
com.example.standardtemplate.app-room-ktx-2.5.0-29 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acfff3a9ad6c33e6b95bb6e437986053\transformed\room-ktx-2.5.0\res
com.example.standardtemplate.app-lifecycle-viewmodel-savedstate-2.6.2-30 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b12a29f053e9072e26debef665623f94\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.example.standardtemplate.app-core-1.13.0-31 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5bcb9b632ef6bbd4d6721f62c9768f1\transformed\core-1.13.0\res
com.example.standardtemplate.app-cardview-1.0.0-32 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9f12821afd075b0266811c2f1b89750\transformed\cardview-1.0.0\res
com.example.standardtemplate.app-appcompat-1.7.0-33 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2425dbf60aad0ac4ec1f17c3e6f842d\transformed\appcompat-1.7.0\res
com.example.standardtemplate.app-material-1.12.0-34 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1a93affe383e101aa07878b40831cae\transformed\material-1.12.0\res
com.example.standardtemplate.app-room-runtime-2.5.0-35 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5bed4fbb8494397dfde4b98e6dcbc27\transformed\room-runtime-2.5.0\res
com.example.standardtemplate.app-lifecycle-livedata-2.6.2-36 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6586212d220b44da5c657735afb6c72\transformed\lifecycle-livedata-2.6.2\res
com.example.standardtemplate.app-glide-4.16.0-37 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da04ef619528daa1d8bb43240abf60d4\transformed\glide-4.16.0\res
com.example.standardtemplate.app-fragment-1.5.4-38 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4b4b501f1dd6c04866e05750abe16c2\transformed\fragment-1.5.4\res
com.example.standardtemplate.app-core-ktx-1.13.0-39 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f52c6dd50fa98e388e7a52bad1567059\transformed\core-ktx-1.13.0\res
com.example.standardtemplate.app-pngs-40 D:\MobileProject\standardtemplate-kotlin\app\build\generated\res\pngs\debug
com.example.standardtemplate.app-res-41 D:\MobileProject\standardtemplate-kotlin\app\build\generated\res\processDebugGoogleServices
com.example.standardtemplate.app-resValues-42 D:\MobileProject\standardtemplate-kotlin\app\build\generated\res\resValues\debug
com.example.standardtemplate.app-packageDebugResources-43 D:\MobileProject\standardtemplate-kotlin\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.standardtemplate.app-packageDebugResources-44 D:\MobileProject\standardtemplate-kotlin\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.standardtemplate.app-merged_res-45 D:\MobileProject\standardtemplate-kotlin\app\build\intermediates\merged_res\debug
com.example.standardtemplate.app-debug-46 D:\MobileProject\standardtemplate-kotlin\app\src\debug\res
com.example.standardtemplate.app-main-47 D:\MobileProject\standardtemplate-kotlin\app\src\main\res
