com.example.standardtemplate.app-core-ktx-1.10.1-0 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\028be9088e76290e676de3e8e181a3f5\transformed\core-ktx-1.10.1\res
com.example.standardtemplate.app-sqlite-framework-2.3.0-1 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0729944f40d4642f73c513ac157afaad\transformed\sqlite-framework-2.3.0\res
com.example.standardtemplate.app-emoji2-1.2.0-2 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\res
com.example.standardtemplate.app-firebase-common-20.3.2-3 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\res
com.example.standardtemplate.app-savedstate-1.2.1-4 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1328923033da8ce6690f733d346fffff\transformed\savedstate-1.2.1\res
com.example.standardtemplate.app-activity-1.7.2-5 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\159b8d9f5694c85ad4e7b68325f3c0d9\transformed\activity-1.7.2\res
com.example.standardtemplate.app-appcompat-1.6.1-6 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21e183407c04a2262ad4621802469c0c\transformed\appcompat-1.6.1\res
com.example.standardtemplate.app-lifecycle-service-2.6.1-7 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2eda3ccc913a5c4927379a64a26037aa\transformed\lifecycle-service-2.6.1\res
com.example.standardtemplate.app-work-runtime-2.8.1-8 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f4307df9b0bac33e0dff561130f96ab\transformed\work-runtime-2.8.1\res
com.example.standardtemplate.app-fragment-1.3.6-9 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30f0018fbcbc1233facba35dbcefa946\transformed\fragment-1.3.6\res
com.example.standardtemplate.app-drawerlayout-1.1.1-10 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35f1c6224769944de111539ab4bfbf5a\transformed\drawerlayout-1.1.1\res
com.example.standardtemplate.app-core-runtime-2.2.0-11 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d4bfe6fe11cd2c4a8eb8da2b922a74a\transformed\core-runtime-2.2.0\res
com.example.standardtemplate.app-startup-runtime-1.1.1-12 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3df6cc11034f0f7e0731682ed2166121\transformed\startup-runtime-1.1.1\res
com.example.standardtemplate.app-play-services-basement-18.1.0-13 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\436b23367b1f7618e21fb32754334ccb\transformed\play-services-basement-18.1.0\res
com.example.standardtemplate.app-firebase-messaging-23.1.2-14 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\res
com.example.standardtemplate.app-recyclerview-1.1.0-15 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c967e6935ddea2d32a85e4bd50b38c3\transformed\recyclerview-1.1.0\res
com.example.standardtemplate.app-lifecycle-livedata-2.6.1-16 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61ad85b7c7ad0ad828891a4eb833da82\transformed\lifecycle-livedata-2.6.1\res
com.example.standardtemplate.app-transition-1.2.0-17 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66ce3c46535ce207f894f0ce27bed42a\transformed\transition-1.2.0\res
com.example.standardtemplate.app-viewpager2-1.0.0-18 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73099662db7bf98f4262269fb8a030e7\transformed\viewpager2-1.0.0\res
com.example.standardtemplate.app-annotation-experimental-1.3.0-19 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c10939a53ca6c9838750a61f31a91ec\transformed\annotation-experimental-1.3.0\res
com.example.standardtemplate.app-lifecycle-viewmodel-savedstate-2.6.1-20 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fe2510b51a76553d0fe01e9c73a8ef8\transformed\lifecycle-viewmodel-savedstate-2.6.1\res
com.example.standardtemplate.app-material-1.9.0-21 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b4e8af462e33194cf40c35189226a44\transformed\material-1.9.0\res
com.example.standardtemplate.app-play-services-base-18.0.1-22 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ddbf4dfd0b92e33e56ede5751f3d034\transformed\play-services-base-18.0.1\res
com.example.standardtemplate.app-lifecycle-process-2.6.1-23 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ed964ef8543b811a25e416fface10a5\transformed\lifecycle-process-2.6.1\res
com.example.standardtemplate.app-emoji2-views-helper-1.2.0-24 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0c5935144b6b2d3cf9265635a0ba28e\transformed\emoji2-views-helper-1.2.0\res
com.example.standardtemplate.app-coordinatorlayout-1.1.0-25 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cb6f6c13d90d9a02baba802ba1cd63\transformed\coordinatorlayout-1.1.0\res
com.example.standardtemplate.app-sqlite-2.3.0-26 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7b1b79dfcdbcbc3025946c780c9e3a6\transformed\sqlite-2.3.0\res
com.example.standardtemplate.app-appcompat-resources-1.6.1-27 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab24daf35747d03209fd7212788c9e68\transformed\appcompat-resources-1.6.1\res
com.example.standardtemplate.app-lifecycle-livedata-core-2.6.1-28 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab329edfa558998c5799f84b34eaaeeb\transformed\lifecycle-livedata-core-2.6.1\res
com.example.standardtemplate.app-cardview-1.0.0-29 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9f12821afd075b0266811c2f1b89750\transformed\cardview-1.0.0\res
com.example.standardtemplate.app-work-runtime-ktx-2.8.1-30 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be30754805b88367f897abb31c4c6219\transformed\work-runtime-ktx-2.8.1\res
com.example.standardtemplate.app-room-runtime-2.5.0-31 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5bed4fbb8494397dfde4b98e6dcbc27\transformed\room-runtime-2.5.0\res
com.example.standardtemplate.app-glide-4.16.0-32 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da04ef619528daa1d8bb43240abf60d4\transformed\glide-4.16.0\res
com.example.standardtemplate.app-lifecycle-runtime-2.6.1-33 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da3bbca233be645ce4c3c6513aef9c3b\transformed\lifecycle-runtime-2.6.1\res
com.example.standardtemplate.app-lifecycle-viewmodel-2.6.1-34 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e360eab0b6efd53b870491edfe323268\transformed\lifecycle-viewmodel-2.6.1\res
com.example.standardtemplate.app-profileinstaller-1.3.0-35 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\res
com.example.standardtemplate.app-constraintlayout-2.1.4-36 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ac015394818289cc056b421c06b91d\transformed\constraintlayout-2.1.4\res
com.example.standardtemplate.app-core-1.10.1-37 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\res
com.example.standardtemplate.app-pngs-38 D:\MobileProject\standardtemplate-kotlin\app\build\generated\res\pngs\debug
com.example.standardtemplate.app-res-39 D:\MobileProject\standardtemplate-kotlin\app\build\generated\res\processDebugGoogleServices
com.example.standardtemplate.app-resValues-40 D:\MobileProject\standardtemplate-kotlin\app\build\generated\res\resValues\debug
com.example.standardtemplate.app-packageDebugResources-41 D:\MobileProject\standardtemplate-kotlin\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.standardtemplate.app-packageDebugResources-42 D:\MobileProject\standardtemplate-kotlin\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.standardtemplate.app-merged_res-43 D:\MobileProject\standardtemplate-kotlin\app\build\intermediates\merged_res\debug
com.example.standardtemplate.app-debug-44 D:\MobileProject\standardtemplate-kotlin\app\src\debug\res
com.example.standardtemplate.app-main-45 D:\MobileProject\standardtemplate-kotlin\app\src\main\res
