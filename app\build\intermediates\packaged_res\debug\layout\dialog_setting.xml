<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:layout_marginLeft="10dp">

    <!-- Dialog Container -->
    <androidx.cardview.widget.CardView
        android:layout_width="330dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="30dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="8dp"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="20dp">

            <!-- Header (Title & Close Button) -->
            <TextView
                android:id="@+id/txtTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/setting"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/primaryColor"
                android:textAlignment="center"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginEnd="16dp" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_close"
                android:contentDescription="@string/close"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/primaryColor" />

            <!-- URLConnection Label -->
            <TextView
                android:id="@+id/txtURLConnection"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/UrlConnection"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/textColor"
                app:layout_constraintTop_toBottomOf="@id/txtTitle"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="24dp" />

            <!-- URL Input Field -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/urlInputLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:boxStrokeColor="@color/primaryColor"
                app:boxStrokeWidth="1dp"
                app:hintTextColor="@color/hintColor"
                app:layout_constraintTop_toBottomOf="@id/txtURLConnection"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginTop="16dp">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtUrl"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/insUrlCon"
                    android:textSize="16sp"
                    android:textColor="@color/textColor"
                    android:inputType="textUri" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- Save Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSave"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/save"
                android:textSize="16sp"
                android:textColor="@color/white"
                android:backgroundTint="@color/primaryColor"
                app:cornerRadius="8dp"
            app:iconGravity="textStart"
            app:iconTint="@color/white"
            app:layout_constraintTop_toBottomOf="@id/urlInputLayout"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="24dp" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>