<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:background="@color/white"
    android:clickable="true"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Machine ID -->
        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txtMachineId"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Machine"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/primaryColor"
            android:padding="4dp"/>

        <!-- Divider -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@android:color/darker_gray"
            android:layout_marginVertical="8dp"/>

        <!-- Error Message -->
        <TextView
            android:id="@+id/txtErrorMsg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="ErrorMsg"
            android:textSize="14sp"
            android:textColor="@color/textColor"
            android:padding="4dp"/>

        <!-- Machine Status -->
        <TextView
            android:id="@+id/txtMachineStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Machine Status"
            android:textSize="14sp"
            android:textColor="@color/textColor"
            android:padding="4dp"/>
    </LinearLayout>
</androidx.cardview.widget.CardView>