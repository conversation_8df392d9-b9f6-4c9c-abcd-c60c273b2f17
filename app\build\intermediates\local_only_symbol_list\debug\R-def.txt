R_DEF: Internal format may change without notice
local
color accentColor
color black
color hintColor
color primaryColor
color primaryDarkColor
color textColor
color white
drawable application_icon
drawable dialog_background
drawable ic_back
drawable ic_close
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_noti_background
drawable input_field_background
id btnAccept
id btnAcceptedTicketList
id btnBack
id btnCancel
id btnClose
id btnConfirm
id btnCreate
id btnDone
id btnLogin
id btnLogout
id btnOk
id btnRegister
id btnReturn
id btnSave
id btnSetting
id btnSubmit
id cardView
id dlCreateTicket
id editErrorMsg
id editMachineId
id editMachineStatus
id edtRemedy
id edtUrl
id errorMsgLayout
id fullNameLayout
id hlLogin
id hlRegister
id linearLayout
id machineIdLayout
id machineStatusLayout
id main
id passwordLayout
id rvAcceptedTicketList
id rvTicketList
id spinner
id spinnerText
id ticketCardView
id tvAttendBy
id tvCompleteAt
id tvCreatedAt
id tvErrorMsg
id tvMachineId
id tvMachineStatus
id tvMessage
id tvStatus
id tvTicketId
id tvTitle
id txtAcceptedby
id txtCreatedAt
id txtCreator
id txtErrorMsg
id txtFullName
id txtHeader
id txtMachineDowntime
id txtMachineId
id txtMachineStatus
id txtPass
id txtPassword
id txtRegion
id txtStatus
id txtTitle
id txtURLConnection
id txtUsername
id urlInputLayout
id usernameLayout
layout activity_accepted_ticket_details
layout activity_accepted_ticket_list
layout activity_login
layout activity_new_ticket_details
layout activity_new_ticket_list
layout activity_register
layout dialog_confirmation
layout dialog_create
layout dialog_message
layout dialog_setting
layout item_accepted_ticket_list
layout recycle_view_new_ticket_list
layout spinner_item
mipmap ic_launcher
mipmap ic_launcher_foreground
mipmap ic_launcher_round
string AT_001
string CT_001
string C_001
string ConnectionError
string DT_001
string ErrorMsg
string F_001
string L_001
string L_002
string L_003
string R_001
string U_001
string UrlConnection
string acceptedTicketList
string actionStr
string app_name
string btnAccept
string btnBack
string btnCreate
string btnLogin
string btnRegister
string cancel
string change_language
string change_language_content1
string change_language_content2
string chgLanguage
string chinese
string close
string confirm
string confirmationStr
string connectionFailed
string connectionFailedMsg
string createSuccess
string createTicket
string english
string errorCode_AT_001
string errorCode_CT_001
string errorCode_C_001
string errorCode_DT_001
string errorCode_F_001
string errorCode_L_001
string errorCode_L_002
string errorCode_L_003
string errorCode_R_001
string errorCode_U_001
string exit
string exitMsg
string failed
string failedMsg
string fullname
string gcm_defaultSenderId
string getUserFailed
string getUserFailedMsg
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string hlLogin
string hlRegister
string insUrlCon
string language
string language_chinese
string language_english
string loginFailed
string loginFailedMsg
string loginSuc
string logout
string logoutMsg
string machStatus
string machineID
string machineStat
string newTicketTitle
string pass
string proceedLogin
string project_id
string region
string register
string reminder
string reminderMsg
string reminderMsg2
string save
string select_language
string setting
string success
string txtCreatedAt
string txtCreator
string txtErrorMsg
string txtMachDowntime
string txtStatus
string txtmachineId
string updConnection
string updateSuccess
string username
string welcMsg
string welcome
style Base.Theme.StandardTemplate
style Theme.StandardTemplate
xml backup_rules
xml data_extraction_rules
xml locales_config
