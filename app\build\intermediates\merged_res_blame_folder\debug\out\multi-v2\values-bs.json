{"logs": [{"outputFile": "com.example.standardtemplate.app-mergeDebugResources-43:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c2425dbf60aad0ac4ec1f17c3e6f842d\\transformed\\appcompat-1.7.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2248,2353,2467,2570,2739,2835", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,105,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2243,2348,2462,2565,2734,2830,2917"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,131", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "377,498,595,702,788,892,1014,1099,1181,1272,1365,1460,1554,1654,1747,1842,1937,2028,2119,2207,2310,2414,2520,2625,2739,2842,3011,11858", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,105,104,113,102,168,95,86", "endOffsets": "493,590,697,783,887,1009,1094,1176,1267,1360,1455,1549,1649,1742,1837,1932,2023,2114,2202,2305,2409,2515,2620,2734,2837,3006,3102,11940"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\436b23367b1f7618e21fb32754334ccb\\transformed\\play-services-basement-18.1.0\\res\\values-bs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "136", "endOffsets": "331"}, "to": {"startLines": "57", "startColumns": "4", "startOffsets": "5564", "endColumns": "140", "endOffsets": "5700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d1a93affe383e101aa07878b40831cae\\transformed\\material-1.12.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,327,406,486,568,670,764,860,986,1067,1129,1195,1287,1364,1427,1535,1595,1661,1717,1788,1848,1902,2021,2078,2140,2194,2269,2393,2481,2558,2652,2736,2819,2964,3049,3135,3268,3356,3434,3488,3542,3608,3682,3760,3831,3913,3985,4062,4135,4205,4314,4407,4479,4571,4667,4741,4817,4913,4966,5048,5115,5202,5289,5351,5415,5478,5547,5655,5760,5861,5964,6022,6080,6160,6246,6322", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,78,79,81,101,93,95,125,80,61,65,91,76,62,107,59,65,55,70,59,53,118,56,61,53,74,123,87,76,93,83,82,144,84,85,132,87,77,53,53,65,73,77,70,81,71,76,72,69,108,92,71,91,95,73,75,95,52,81,66,86,86,61,63,62,68,107,104,100,102,57,57,79,85,75,76", "endOffsets": "322,401,481,563,665,759,855,981,1062,1124,1190,1282,1359,1422,1530,1590,1656,1712,1783,1843,1897,2016,2073,2135,2189,2264,2388,2476,2553,2647,2731,2814,2959,3044,3130,3263,3351,3429,3483,3537,3603,3677,3755,3826,3908,3980,4057,4130,4200,4309,4402,4474,4566,4662,4736,4812,4908,4961,5043,5110,5197,5284,5346,5410,5473,5542,5650,5755,5856,5959,6017,6075,6155,6241,6317,6394"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,132,133,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3107,3186,3266,3348,3450,4269,4365,4491,6765,6827,6893,6985,7062,7125,7233,7293,7359,7415,7486,7546,7600,7719,7776,7838,7892,7967,8091,8179,8256,8350,8434,8517,8662,8747,8833,8966,9054,9132,9186,9240,9306,9380,9458,9529,9611,9683,9760,9833,9903,10012,10105,10177,10269,10365,10439,10515,10611,10664,10746,10813,10900,10987,11049,11113,11176,11245,11353,11458,11559,11662,11720,11778,11945,12031,12107", "endLines": "6,34,35,36,37,38,46,47,48,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,132,133,134", "endColumns": "12,78,79,81,101,93,95,125,80,61,65,91,76,62,107,59,65,55,70,59,53,118,56,61,53,74,123,87,76,93,83,82,144,84,85,132,87,77,53,53,65,73,77,70,81,71,76,72,69,108,92,71,91,95,73,75,95,52,81,66,86,86,61,63,62,68,107,104,100,102,57,57,79,85,75,76", "endOffsets": "372,3181,3261,3343,3445,3539,4360,4486,4567,6822,6888,6980,7057,7120,7228,7288,7354,7410,7481,7541,7595,7714,7771,7833,7887,7962,8086,8174,8251,8345,8429,8512,8657,8742,8828,8961,9049,9127,9181,9235,9301,9375,9453,9524,9606,9678,9755,9828,9898,10007,10100,10172,10264,10360,10434,10510,10606,10659,10741,10808,10895,10982,11044,11108,11171,11240,11348,11453,11554,11657,11715,11773,11853,12026,12102,12179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8ddbf4dfd0b92e33e56ede5751f3d034\\transformed\\play-services-base-18.0.1\\res\\values-bs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,573,679,825,947,1055,1153,1303,1406,1563,1686,1832,1974,2038,2096", "endColumns": "101,155,121,105,145,121,107,97,149,102,156,122,145,141,63,57,80", "endOffsets": "294,450,572,678,824,946,1054,1152,1302,1405,1562,1685,1831,1973,2037,2095,2176"}, "to": {"startLines": "49,50,51,52,53,54,55,56,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4572,4678,4838,4964,5074,5224,5350,5462,5705,5859,5966,6127,6254,6404,6550,6618,6680", "endColumns": "105,159,125,109,149,125,111,101,153,106,160,126,149,145,67,61,84", "endOffsets": "4673,4833,4959,5069,5219,5345,5457,5559,5854,5961,6122,6249,6399,6545,6613,6675,6760"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b5bcb9b632ef6bbd4d6721f62c9768f1\\transformed\\core-1.13.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "39,40,41,42,43,44,45,135", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3544,3642,3744,3842,3946,4050,4152,12184", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "3637,3739,3837,3941,4045,4147,4264,12280"}}]}]}