<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res"><file name="application_icon" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\drawable\application_icon.png" qualifiers="" type="drawable"/><file name="dialog_background" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\drawable\dialog_background.xml" qualifiers="" type="drawable"/><file name="ic_back" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\drawable\ic_back.xml" qualifiers="" type="drawable"/><file name="ic_close" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\drawable\ic_close.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_noti_background" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\drawable\ic_noti_background.xml" qualifiers="" type="drawable"/><file name="input_field_background" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\drawable\input_field_background.xml" qualifiers="" type="drawable"/><file name="activity_accepted_ticket_details" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\layout\activity_accepted_ticket_details.xml" qualifiers="" type="layout"/><file name="activity_accepted_ticket_list" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\layout\activity_accepted_ticket_list.xml" qualifiers="" type="layout"/><file name="activity_login" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="activity_new_ticket_details" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\layout\activity_new_ticket_details.xml" qualifiers="" type="layout"/><file name="activity_new_ticket_list" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\layout\activity_new_ticket_list.xml" qualifiers="" type="layout"/><file name="activity_register" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\layout\activity_register.xml" qualifiers="" type="layout"/><file name="dialog_confirmation" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\layout\dialog_confirmation.xml" qualifiers="" type="layout"/><file name="dialog_create" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\layout\dialog_create.xml" qualifiers="" type="layout"/><file name="dialog_message" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\layout\dialog_message.xml" qualifiers="" type="layout"/><file name="dialog_setting" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\layout\dialog_setting.xml" qualifiers="" type="layout"/><file name="item_accepted_ticket_list" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\layout\item_accepted_ticket_list.xml" qualifiers="" type="layout"/><file name="recycle_view_new_ticket_list" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\layout\recycle_view_new_ticket_list.xml" qualifiers="" type="layout"/><file name="spinner_item" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\layout\spinner_item.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primaryColor">#6200EE</color><color name="primaryDarkColor">#3700B3</color><color name="accentColor">#03DAC5</color><color name="textColor">#000000</color><color name="hintColor">#808080</color></file><file path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\values\strings.xml" qualifiers=""><string name="select_language">Select Language</string><string name="english">English</string><string name="chinese">Chinese</string><string name="app_name">Standard Template</string><string name="welcome">Welcome!</string><string name="username">username</string><string name="pass">Password</string><string name="btnLogin">Login</string><string name="setting">Setting</string><string name="hlRegister">Register? Click Here</string><string name="newTicketTitle">View New Ticket Details</string><string name="txtmachineId">"Machine ID: "</string><string name="machStatus">"Machine Status: "</string><string name="txtErrorMsg">"Error Message: "</string><string name="txtCreatedAt">"Created At: "</string><string name="txtCreator">"Created By: "</string><string name="txtStatus">"Status: "</string><string name="txtMachDowntime">"Machine Downtime: "</string><string name="btnAccept">Accept</string><string name="btnBack">Back</string><string name="chgLanguage">Change Language</string><string name="language_english">English</string><string name="language_chinese">Chinese</string><string name="region">"Region: "</string><string name="btnCreate">Create +</string><string name="acceptedTicketList">Accepted Ticket List</string><string name="logout">Logout</string><string name="change_language">Language Change</string><string name="change_language_content1">Change language to </string><string name="change_language_content2">Restart Application is required</string><string name="loginSuc">Login Successful</string><string name="welcMsg">Welcome Back!</string><string name="loginFailed">Login Failed</string><string name="loginFailedMsg">Wrong Username or Password!</string><string name="getUserFailed">Something Wrong</string><string name="getUserFailedMsg">Failed to get user information!</string><string name="connectionFailed">Connection Failed</string><string name="connectionFailedMsg">"Network Error: "</string><string name="logoutMsg">, Are you sure you want to logout?</string><string name="success">Success</string><string name="failed">Failed</string><string name="createSuccess">Ticket Created Successfully!</string><string name="failedMsg">Something Wrong! Please Try Again!</string><string name="updConnection">Url Connection Updated To:</string><string name="ConnectionError">URL cannot be empty, must start with http and end with /</string><string name="updateSuccess">You Have Accepted 1 Ticket!</string><string name="UrlConnection">URLConnection</string><string name="insUrlCon">Enter URL</string><string name="save">Save</string><string name="createTicket">Create New Ticket</string><string name="machineID">Enter Machine ID</string><string name="machineStat">Enter Machine Status</string><string name="ErrorMsg">Enter Error Message</string><string name="cancel">Cancel</string><string name="confirm">Confirm</string><string name="actionStr">Confirm Action</string><string name="confirmationStr">Are you sure you want to proceed?</string><string name="register">Register An Account</string><string name="fullname">Fullname</string><string name="btnRegister">Register</string><string name="hlLogin">Have an account? Click Here</string><string name="close">Close</string><string name="reminder">Reminder</string><string name="reminderMsg">Username or Password cannot be empty!</string><string name="reminderMsg2">All fields must be filled!</string><string name="proceedLogin">Please Proceed to Login!</string><string name="language">"Language: "</string><string name="exitMsg">Are You Sure Want to Exit From App?</string><string name="exit">Exit</string><string name="L_001">L_001</string><string name="L_002">L_002</string><string name="L_003">L_003</string><string name="errorCode_L_001">Invalid login credential.</string><string name="errorCode_L_002">Username is required.</string><string name="errorCode_L_003">Password is required.</string><string name="R_001">R_001</string><string name="errorCode_R_001">Register Failed.</string><string name="C_001">C_001</string><string name="errorCode_C_001">Server connection error!</string><string name="U_001">U_001</string><string name="errorCode_U_001">Failed to Retrieve User Info.</string><string name="CT_001">T_001</string><string name="errorCode_CT_001">Ticket Creation Failed.</string><string name="AT_001">AT_001</string><string name="errorCode_AT_001">Ticket Accept Failed.</string><string name="DT_001">DT_001</string><string name="errorCode_DT_001">Failed to submit request! Please Try Again!</string><string name="F_001">F_001</string><string name="errorCode_F_001">Failed to get Device Token!</string></file><file path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.StandardTemplate" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.StandardTemplate" parent="Base.Theme.StandardTemplate"/></file><file path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.StandardTemplate" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\values-zh\strings.xml" qualifiers="zh"><string name="select_language">选择语言</string><string name="english">英语</string><string name="chinese">中文</string><string name="app_name">标准模板</string><string name="welcome">欢迎！</string><string name="username">用户名</string><string name="pass">密码</string><string name="btnLogin">登录</string><string name="setting">设置</string><string name="hlRegister">注册？点击这里</string><string name="newTicketTitle">查看新工单详情</string><string name="txtmachineId">"机器编号: "</string><string name="machStatus">"机器状态: "</string><string name="txtErrorMsg">"错误信息: "</string><string name="txtCreatedAt">"创建时间: "</string><string name="txtCreator">"创建者: "</string><string name="txtStatus">"状态: "</string><string name="txtMachDowntime">"机器停机时间: "</string><string name="btnAccept">接受</string><string name="btnBack">返回</string><string name="chgLanguage">更改语言</string><string name="language_english">英语</string><string name="language_chinese">中文</string><string name="region">"地区: "</string><string name="btnCreate">创建 +</string><string name="acceptedTicketList">已接受工单列表</string><string name="logout">退出登录</string><string name="change_language">更改语言</string><string name="change_language_content1">更改语言为</string><string name="change_language_content2">需要重启应用</string><string name="loginSuc">登录成功</string><string name="welcMsg">欢迎回来！</string><string name="loginFailed">登录失败</string><string name="loginFailedMsg">用户名或密码错误！</string><string name="getUserFailed">发生错误</string><string name="getUserFailedMsg">获取用户信息失败！</string><string name="connectionFailed">连接失败</string><string name="connectionFailedMsg">"网络错误: "</string><string name="logoutMsg">，您确定要退出登录吗？</string><string name="success">成功</string><string name="failed">失败</string><string name="createSuccess">工单创建成功！</string><string name="failedMsg">发生错误！请重试！</string><string name="updConnection">URL 连接已更新为:</string><string name="ConnectionError">URL不能为空，且必须以http开头, /结尾</string><string name="updateSuccess">您已接受 1 个工单！</string><string name="UrlConnection">URL 连接</string><string name="insUrlCon">输入 URL</string><string name="save">保存</string><string name="createTicket">创建新工单</string><string name="machineID">输入机器编号</string><string name="machineStat">输入机器状态</string><string name="ErrorMsg">输入错误信息</string><string name="cancel">取消</string><string name="confirm">确认</string><string name="actionStr">确认操作</string><string name="confirmationStr">您确定要继续吗？</string><string name="register">注册账户</string><string name="fullname">全名</string><string name="btnRegister">注册</string><string name="hlLogin">已有账户？点击这里</string><string name="close">关闭</string><string name="reminder">提醒</string><string name="reminderMsg">用户名或密码不能为空！</string><string name="reminderMsg2">所有字段都必须填写！</string><string name="proceedLogin">请前往登录页面继续登录！</string><string name="language">"语言： "</string><string name="exitMsg">确定退出此应用？</string><string name="exit">退出</string><string name="L_001">L_001</string><string name="L_002">L_002</string><string name="L_003">L_003</string><string name="errorCode_L_003">密码为必填项。</string><string name="R_001">R_001</string><string name="errorCode_R_001">注册失败。</string><string name="C_001">C_001</string><string name="errorCode_C_001">服务器连接错误！</string><string name="U_001">U_001</string><string name="errorCode_U_001">获取用户信息失败。</string><string name="CT_001">T_001</string><string name="errorCode_CT_001">创建工单失败。</string><string name="AT_001">A_001</string><string name="errorCode_AT_001">接受工单失败。</string><string name="DT_001">DT_001</string><string name="errorCode_L_001">登录凭证无效。</string><string name="errorCode_L_002">用户名为必填项。</string><string name="errorCode_DT_001">提交请求失败！请再试一次！</string></file><file name="backup_rules" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="locales_config" path="D:\MobileProject\standardtemplate-kotlin\app\src\main\res\xml\locales_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\MobileProject\standardtemplate-kotlin\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\MobileProject\standardtemplate-kotlin\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\MobileProject\standardtemplate-kotlin\app\build\generated\res\resValues\debug"/><source path="D:\MobileProject\standardtemplate-kotlin\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\MobileProject\standardtemplate-kotlin\app\build\generated\res\resValues\debug"/><source path="D:\MobileProject\standardtemplate-kotlin\app\build\generated\res\processDebugGoogleServices"><file path="D:\MobileProject\standardtemplate-kotlin\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">65703032838</string><string name="google_api_key" translatable="false">AIzaSyCHTf_XCagTF2NfovuhL85D7szq0DOCduU</string><string name="google_app_id" translatable="false">1:65703032838:android:2425e9aa5d3deea866ab87</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyCHTf_XCagTF2NfovuhL85D7szq0DOCduU</string><string name="google_storage_bucket" translatable="false">standardtemplate-f5070.firebasestorage.app</string><string name="project_id" translatable="false">standardtemplate-f5070</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>