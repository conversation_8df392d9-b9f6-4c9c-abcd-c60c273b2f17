{"logs": [{"outputFile": "com.example.standardtemplate.app-mergeDebugResources-42:/values-zh/values-zh.xml", "map": [{"source": "D:\\MobileProject\\standardtemplate-kotlin\\app\\src\\main\\res\\values-zh\\strings.xml", "from": {"startLines": "82,80,76,45,84,53,70,71,72,74,78,47,26,56,4,19,20,25,8,60,54,28,29,30,21,3,62,55,57,37,38,42,50,2,83,81,77,87,85,86,73,75,79,69,68,41,43,59,35,36,61,10,48,67,23,22,33,34,31,27,39,13,51,52,11,7,66,24,58,63,64,65,49,1,9,40,15,16,14,18,17,12,44,46,6,32,5", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3826,3730,3539,2107,3922,2506,3271,3312,3353,3447,3634,2231,1155,2630,147,842,884,1111,310,2812,2551,1252,1302,1362,924,107,2903,2590,2674,1713,1764,1952,2364,67,3868,3772,3580,4072,3965,4018,3394,3488,3675,3234,3188,1913,2003,2771,1609,1657,2856,391,2281,3143,1019,970,1509,1555,1423,1211,1822,542,2412,2458,439,273,3088,1068,2728,2941,2982,3035,2327,17,351,1873,641,692,591,786,740,491,2052,2176,231,1466,190", "endColumns": "40,40,39,67,41,43,39,39,39,39,39,48,54,42,41,40,38,42,39,42,37,48,58,59,44,38,36,38,52,49,56,49,46,38,52,52,52,58,51,52,51,49,53,35,44,37,47,39,46,54,45,46,44,43,47,47,44,52,41,39,49,47,44,46,50,35,53,41,41,39,51,51,35,48,38,38,49,46,48,54,44,49,53,53,40,41,39", "endOffsets": "3862,3766,3574,2170,3959,2545,3306,3347,3388,3482,3669,2275,1205,2668,184,878,918,1149,345,2850,2584,1296,1356,1417,964,141,2935,2624,2722,1758,1816,1997,2406,101,3916,3820,3628,4126,4012,4066,3441,3533,3724,3265,3228,1946,2046,2806,1651,1707,2897,433,2321,3182,1062,1013,1549,1603,1460,1246,1867,585,2452,2500,485,304,3137,1105,2765,2976,3029,3082,2358,61,385,1907,686,734,635,836,780,536,2101,2225,267,1503,225"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,96,137,177,245,287,331,371,411,451,491,531,580,635,678,720,761,800,843,883,926,964,1013,1072,1132,1177,1216,1253,1292,1345,1395,1452,1502,1549,1588,1641,1694,1747,1806,1858,1911,1963,2013,2067,2103,2148,2186,2234,2274,2321,2376,2422,2469,2514,2558,2606,2654,2699,2752,2794,2834,2884,2932,2977,3024,3075,3111,3165,3207,3249,3289,3341,3393,3429,3478,3517,3556,3606,3653,3702,3757,3802,3852,3906,3960,4001,4043", "endColumns": "40,40,39,67,41,43,39,39,39,39,39,48,54,42,41,40,38,42,39,42,37,48,58,59,44,38,36,38,52,49,56,49,46,38,52,52,52,58,51,52,51,49,53,35,44,37,47,39,46,54,45,46,44,43,47,47,44,52,41,39,49,47,44,46,50,35,53,41,41,39,51,51,35,48,38,38,49,46,48,54,44,49,53,53,40,41,39", "endOffsets": "91,132,172,240,282,326,366,406,446,486,526,575,630,673,715,756,795,838,878,921,959,1008,1067,1127,1172,1211,1248,1287,1340,1390,1447,1497,1544,1583,1636,1689,1742,1801,1853,1906,1958,2008,2062,2098,2143,2181,2229,2269,2316,2371,2417,2464,2509,2553,2601,2649,2694,2747,2789,2829,2879,2927,2972,3019,3070,3106,3160,3202,3244,3284,3336,3388,3424,3473,3512,3551,3601,3648,3697,3752,3797,3847,3901,3955,3996,4038,4078"}}]}]}