<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accentColor">#03DAC5</color>
    <color name="black">#FF000000</color>
    <color name="hintColor">#808080</color>
    <color name="primaryColor">#6200EE</color>
    <color name="primaryDarkColor">#3700B3</color>
    <color name="textColor">#000000</color>
    <color name="white">#FFFFFFFF</color>
    <string name="AT_001">AT_001</string>
    <string name="CT_001">T_001</string>
    <string name="C_001">C_001</string>
    <string name="ConnectionError">URL cannot be empty, must start with http and end with /</string>
    <string name="DT_001">DT_001</string>
    <string name="ErrorMsg">Enter Error Message</string>
    <string name="F_001">F_001</string>
    <string name="L_001">L_001</string>
    <string name="L_002">L_002</string>
    <string name="L_003">L_003</string>
    <string name="R_001">R_001</string>
    <string name="U_001">U_001</string>
    <string name="UrlConnection">URLConnection</string>
    <string name="acceptedTicketList">Accepted Ticket List</string>
    <string name="actionStr">Confirm Action</string>
    <string name="app_name">Standard Template</string>
    <string name="btnAccept">Accept</string>
    <string name="btnBack">Back</string>
    <string name="btnCreate">Create +</string>
    <string name="btnLogin">Login</string>
    <string name="btnRegister">Register</string>
    <string name="cancel">Cancel</string>
    <string name="change_language">Language Change</string>
    <string name="change_language_content1">Change language to </string>
    <string name="change_language_content2">Restart Application is required</string>
    <string name="chgLanguage">Change Language</string>
    <string name="chinese">Chinese</string>
    <string name="close">Close</string>
    <string name="confirm">Confirm</string>
    <string name="confirmationStr">Are you sure you want to proceed?</string>
    <string name="connectionFailed">Connection Failed</string>
    <string name="connectionFailedMsg">"Network Error: "</string>
    <string name="createSuccess">Ticket Created Successfully!</string>
    <string name="createTicket">Create New Ticket</string>
    <string name="english">English</string>
    <string name="errorCode_AT_001">Ticket Accept Failed.</string>
    <string name="errorCode_CT_001">Ticket Creation Failed.</string>
    <string name="errorCode_C_001">Server connection error!</string>
    <string name="errorCode_DT_001">Failed to submit request! Please Try Again!</string>
    <string name="errorCode_F_001">Failed to get Device Token!</string>
    <string name="errorCode_L_001">Invalid login credential.</string>
    <string name="errorCode_L_002">Username is required.</string>
    <string name="errorCode_L_003">Password is required.</string>
    <string name="errorCode_R_001">Register Failed.</string>
    <string name="errorCode_U_001">Failed to Retrieve User Info.</string>
    <string name="exit">Exit</string>
    <string name="exitMsg">Are You Sure Want to Exit From App?</string>
    <string name="failed">Failed</string>
    <string name="failedMsg">Something Wrong! Please Try Again!</string>
    <string name="fullname">Fullname</string>
    <string name="gcm_defaultSenderId" translatable="false">***********</string>
    <string name="getUserFailed">Something Wrong</string>
    <string name="getUserFailedMsg">Failed to get user information!</string>
    <string name="google_api_key" translatable="false">AIzaSyCHTf_XCagTF2NfovuhL85D7szq0DOCduU</string>
    <string name="google_app_id" translatable="false">1:***********:android:2425e9aa5d3deea866ab87</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyCHTf_XCagTF2NfovuhL85D7szq0DOCduU</string>
    <string name="google_storage_bucket" translatable="false">standardtemplate-f5070.firebasestorage.app</string>
    <string name="hlLogin">Have an account? Click Here</string>
    <string name="hlRegister">Register? Click Here</string>
    <string name="insUrlCon">Enter URL</string>
    <string name="language">"Language: "</string>
    <string name="language_chinese">Chinese</string>
    <string name="language_english">English</string>
    <string name="loginFailed">Login Failed</string>
    <string name="loginFailedMsg">Wrong Username or Password!</string>
    <string name="loginSuc">Login Successful</string>
    <string name="logout">Logout</string>
    <string name="logoutMsg">, Are you sure you want to logout?</string>
    <string name="machStatus">"Machine Status: "</string>
    <string name="machineID">Enter Machine ID</string>
    <string name="machineStat">Enter Machine Status</string>
    <string name="newTicketTitle">View New Ticket Details</string>
    <string name="pass">Password</string>
    <string name="proceedLogin">Please Proceed to Login!</string>
    <string name="project_id" translatable="false">standardtemplate-f5070</string>
    <string name="region">"Region: "</string>
    <string name="register">Register An Account</string>
    <string name="reminder">Reminder</string>
    <string name="reminderMsg">Username or Password cannot be empty!</string>
    <string name="reminderMsg2">All fields must be filled!</string>
    <string name="save">Save</string>
    <string name="select_language">Select Language</string>
    <string name="setting">Setting</string>
    <string name="success">Success</string>
    <string name="txtCreatedAt">"Created At: "</string>
    <string name="txtCreator">"Created By: "</string>
    <string name="txtErrorMsg">"Error Message: "</string>
    <string name="txtMachDowntime">"Machine Downtime: "</string>
    <string name="txtStatus">"Status: "</string>
    <string name="txtmachineId">"Machine ID: "</string>
    <string name="updConnection">Url Connection Updated To:</string>
    <string name="updateSuccess">You Have Accepted 1 Ticket!</string>
    <string name="username">username</string>
    <string name="welcMsg">Welcome Back!</string>
    <string name="welcome">Welcome!</string>
    <style name="Base.Theme.StandardTemplate" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style>
    <style name="Theme.StandardTemplate" parent="Base.Theme.StandardTemplate"/>
</resources>