<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="20dp"
    app:cardElevation="8dp"
    app:cardBackgroundColor="@android:color/white"
    app:cardPreventCornerOverlap="false"
    android:clipChildren="true"
    android:clipToPadding="true"
    android:id="@+id/dlCreateTicket"
    android:layout_gravity="center"
    android:layout_margin="20dp">

    <LinearLayout
        android:layout_width="370dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        android:background="@android:color/transparent"
        android:clipChildren="true"
        android:clipToPadding="true">

        <!-- Title -->
        <TextView
            android:id="@+id/txtTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/createTicket"
            android:textAlignment="center"
            android:textAllCaps="true"
            android:textSize="22sp"
            android:textStyle="bold"
            android:textColor="@color/primaryColor"
            android:layout_marginBottom="24dp"
            android:layout_marginTop="10dp"/>

        <!-- Machine ID Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/machineIdLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:boxStrokeColor="@color/primaryColor"
            app:boxStrokeWidth="1dp"
            app:hintTextColor="@color/hintColor"
            android:layout_marginBottom="16dp">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editMachineId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/machineID"
                android:textSize="16sp"
                android:textColor="@color/textColor"
                android:inputType="text" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Machine Status Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/machineStatusLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:boxStrokeColor="@color/primaryColor"
            app:boxStrokeWidth="1dp"
            app:hintTextColor="@color/hintColor"
            android:layout_marginBottom="16dp">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editMachineStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/machineStat"
                android:textSize="16sp"
                android:textColor="@color/textColor"
                android:inputType="text" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Error Message Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/errorMsgLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:boxStrokeColor="@color/primaryColor"
            app:boxStrokeWidth="1dp"
            app:hintTextColor="@color/hintColor"
            android:layout_marginBottom="24dp">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editErrorMsg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/ErrorMsg"
                android:textSize="16sp"
                android:textColor="@color/textColor"
                android:inputType="text" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Horizontal layout for buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:gravity="end"
            android:orientation="horizontal">

            <!-- Cancel Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnCancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:backgroundTint="@color/primaryDarkColor"
                android:text="@string/cancel"
                android:textColor="@color/white"
                app:cornerRadius="8dp" />

            <!-- Submit Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSubmit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/confirm"
                android:textColor="@color/white"
                android:backgroundTint="@color/primaryColor"
                app:cornerRadius="8dp" />
        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>