1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.standardtemplate"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="33" />
10    <!-- Permissions for network access -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
13-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:7:5-76
13-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:7:22-73
14    <!-- Foreground service permissions -->
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
15-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:9:5-77
15-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:9:22-74
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
16-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:10:5-87
16-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:10:22-84
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
17-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:11:5-77
17-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:11:22-74
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
18-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:12:5-86
18-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:12:22-83
19    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
19-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:13:5-79
19-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:13:22-76
20    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
20-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:14:5-81
20-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:14:22-78
21    <!-- background permission -->
22    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
22-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:16:5-95
22-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:16:22-92
23    <uses-permission android:name="android.permission.WAKE_LOCK" />
23-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:17:5-68
23-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:17:22-65
24    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
24-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:18:5-81
24-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:18:22-78
25    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
25-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:19:5-92
25-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:19:22-89
26    <!-- storage permission -->
27    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
27-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:21:5-80
27-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:21:22-77
28    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
28-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:22:5-81
28-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:22:22-78
29
30    <!-- Required by older versions of Google Play services to create IID tokens -->
31    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
31-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:28:5-82
31-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:28:22-79
32    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
32-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:26:5-110
32-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:26:22-107
33    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
33-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:25:5-79
33-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:25:22-76
34
35    <permission
35-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
36        android:name="com.example.standardtemplate.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
36-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
37        android:protectionLevel="signature" />
37-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
38
39    <uses-permission android:name="com.example.standardtemplate.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
39-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
39-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
40
41    <application
41-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:24:5-78:19
42        android:name="com.example.standardtemplate.Libraries.LanguageSetting.StandardTemplate"
42-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:25:9-67
43        android:allowBackup="true"
43-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:26:9-35
44        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
44-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
45        android:dataExtractionRules="@xml/data_extraction_rules"
45-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:27:9-65
46        android:debuggable="true"
47        android:extractNativeLibs="false"
48        android:fullBackupContent="@xml/backup_rules"
48-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:28:9-54
49        android:icon="@mipmap/ic_launcher"
49-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:29:9-43
50        android:label="@string/app_name"
50-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:30:9-41
51        android:requestLegacyExternalStorage="true"
51-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:31:9-52
52        android:roundIcon="@mipmap/ic_launcher_round"
52-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:32:9-54
53        android:supportsRtl="true"
53-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:33:9-35
54        android:testOnly="true"
55        android:theme="@style/Theme.StandardTemplate"
55-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:34:9-54
56        android:usesCleartextTraffic="true" >
56-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:35:9-44
57        <activity
57-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:37:9-39:40
58            android:name="com.example.standardtemplate.Activities.Register.RegisterActivity"
58-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:38:13-65
59            android:exported="false" />
59-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:39:13-37
60        <activity
60-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:40:9-42:40
61            android:name="com.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity"
61-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:41:13-81
62            android:exported="false" />
62-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:42:13-37
63        <activity
63-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:43:9-45:40
64            android:name="com.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity"
64-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:44:13-84
65            android:exported="false" />
65-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:45:13-37
66        <activity
66-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:46:9-48:40
67            android:name="com.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity"
67-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:47:13-75
68            android:exported="false" />
68-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:48:13-37
69        <activity
69-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:49:9-51:40
70            android:name="com.example.standardtemplate.Activities.NewTickets.NewTicketListActivity"
70-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:50:13-72
71            android:exported="false" />
71-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:51:13-37
72        <activity
72-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:52:9-60:20
73            android:name="com.example.standardtemplate.Activities.Login_Setting.LoginActivity"
73-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:53:13-67
74            android:exported="true" >
74-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:54:13-36
75            <intent-filter>
75-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:55:13-59:29
76                <action android:name="android.intent.action.MAIN" />
76-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:56:17-69
76-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:56:25-66
77
78                <category android:name="android.intent.category.LAUNCHER" />
78-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:58:17-77
78-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:58:27-74
79            </intent-filter>
80        </activity>
81
82        <service
82-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:62:9-65:56
83            android:name="com.example.standardtemplate.Libraries.TicketMonitoring"
83-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:63:13-55
84            android:exported="false"
84-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:64:13-37
85            android:foregroundServiceType="dataSync" />
85-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:65:13-53
86        <service
86-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:66:9-69:61
87            android:name="com.example.standardtemplate.Libraries.NotificationService"
87-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:67:13-58
88            android:exported="false"
88-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:68:13-37
89            android:foregroundServiceType="mediaPlayback" />
89-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:69:13-58
90        <!-- Implement Firebase messaging service -->
91        <service
91-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:71:9-77:19
92            android:name="com.example.standardtemplate.Libraries.FirebaseMessagingService"
92-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:72:13-63
93            android:exported="false" >
93-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:73:13-37
94            <intent-filter>
94-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:74:13-76:29
95                <action android:name="com.google.firebase.MESSAGING_EVENT" />
95-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:75:17-78
95-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:75:25-75
96            </intent-filter>
97        </service>
98
99        <receiver
99-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:31:9-38:20
100            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
100-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:32:13-78
101            android:exported="true"
101-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:33:13-36
102            android:permission="com.google.android.c2dm.permission.SEND" >
102-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:34:13-73
103            <intent-filter>
103-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:35:13-37:29
104                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
104-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:36:17-81
104-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:36:25-78
105            </intent-filter>
106        </receiver>
107        <!--
108             FirebaseMessagingService performs security checks at runtime,
109             but set to not exported to explicitly avoid allowing another app to call it.
110        -->
111        <service
111-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:44:9-51:19
112            android:name="com.google.firebase.messaging.FirebaseMessagingService"
112-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:45:13-82
113            android:directBootAware="true"
113-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:46:13-43
114            android:exported="false" >
114-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:47:13-37
115            <intent-filter android:priority="-500" >
115-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:74:13-76:29
116                <action android:name="com.google.firebase.MESSAGING_EVENT" />
116-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:75:17-78
116-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:75:25-75
117            </intent-filter>
118        </service>
119        <service
119-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:52:9-58:19
120            android:name="com.google.firebase.components.ComponentDiscoveryService"
120-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:53:13-84
121            android:directBootAware="true"
121-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:34:13-43
122            android:exported="false" >
122-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:54:13-37
123            <meta-data
123-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:55:13-57:85
124                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
124-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:56:17-119
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:57:17-82
126            <meta-data
126-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:31:13-33:85
127                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
127-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:32:17-139
128                android:value="com.google.firebase.components.ComponentRegistrar" />
128-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:33:17-82
129            <meta-data
129-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b17c4ad5726bcee640ce67fecfa959aa\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
130                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
130-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b17c4ad5726bcee640ce67fecfa959aa\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
131                android:value="com.google.firebase.components.ComponentRegistrar" />
131-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b17c4ad5726bcee640ce67fecfa959aa\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
132            <meta-data
132-->[com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\378eae791bdb3069d99d14a82ce883c7\transformed\firebase-installations-17.1.3\AndroidManifest.xml:17:13-19:85
133                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
133-->[com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\378eae791bdb3069d99d14a82ce883c7\transformed\firebase-installations-17.1.3\AndroidManifest.xml:18:17-127
134                android:value="com.google.firebase.components.ComponentRegistrar" />
134-->[com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\378eae791bdb3069d99d14a82ce883c7\transformed\firebase-installations-17.1.3\AndroidManifest.xml:19:17-82
135        </service>
136
137        <receiver
137-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:29:9-33:20
138            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
138-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:30:13-85
139            android:enabled="true"
139-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:31:13-35
140            android:exported="false" >
140-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:32:13-37
141        </receiver>
142
143        <service
143-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:35:9-38:40
144            android:name="com.google.android.gms.measurement.AppMeasurementService"
144-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:36:13-84
145            android:enabled="true"
145-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:37:13-35
146            android:exported="false" />
146-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:38:13-37
147        <service
147-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:39:9-43:72
148            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
148-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:40:13-87
149            android:enabled="true"
149-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:41:13-35
150            android:exported="false"
150-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:42:13-37
151            android:permission="android.permission.BIND_JOB_SERVICE" />
151-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:43:13-69
152
153        <activity
153-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ddbf4dfd0b92e33e56ede5751f3d034\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
154            android:name="com.google.android.gms.common.api.GoogleApiActivity"
154-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ddbf4dfd0b92e33e56ede5751f3d034\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
155            android:exported="false"
155-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ddbf4dfd0b92e33e56ede5751f3d034\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
156            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
156-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ddbf4dfd0b92e33e56ede5751f3d034\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
157
158        <provider
158-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:25:9-30:39
159            android:name="com.google.firebase.provider.FirebaseInitProvider"
159-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:26:13-77
160            android:authorities="com.example.standardtemplate.firebaseinitprovider"
160-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:27:13-72
161            android:directBootAware="true"
161-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:28:13-43
162            android:exported="false"
162-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:29:13-37
163            android:initOrder="100" />
163-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:30:13-36
164
165        <meta-data
165-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\436b23367b1f7618e21fb32754334ccb\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
166            android:name="com.google.android.gms.version"
166-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\436b23367b1f7618e21fb32754334ccb\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
167            android:value="@integer/google_play_services_version" />
167-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\436b23367b1f7618e21fb32754334ccb\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
168
169        <provider
169-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
170            android:name="androidx.startup.InitializationProvider"
170-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
171            android:authorities="com.example.standardtemplate.androidx-startup"
171-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
172            android:exported="false" >
172-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
173            <meta-data
173-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
174                android:name="androidx.emoji2.text.EmojiCompatInitializer"
174-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
175                android:value="androidx.startup" />
175-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
176            <meta-data
176-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
177                android:name="androidx.work.WorkManagerInitializer"
177-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
178                android:value="androidx.startup" />
178-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
179            <meta-data
179-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ed964ef8543b811a25e416fface10a5\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
180                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
180-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ed964ef8543b811a25e416fface10a5\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
181                android:value="androidx.startup" />
181-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ed964ef8543b811a25e416fface10a5\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
182            <meta-data
182-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
183                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
183-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
184                android:value="androidx.startup" />
184-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
185        </provider>
186
187        <service
187-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
188            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
188-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
189            android:directBootAware="false"
189-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
190            android:enabled="@bool/enable_system_alarm_service_default"
190-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
191            android:exported="false" />
191-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
192        <service
192-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
193            android:name="androidx.work.impl.background.systemjob.SystemJobService"
193-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
194            android:directBootAware="false"
194-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
195            android:enabled="@bool/enable_system_job_service_default"
195-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
196            android:exported="true"
196-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
197            android:permission="android.permission.BIND_JOB_SERVICE" />
197-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
198        <service
198-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
199            android:name="androidx.work.impl.foreground.SystemForegroundService"
199-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
200            android:directBootAware="false"
200-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
201            android:enabled="@bool/enable_system_foreground_service_default"
201-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
202            android:exported="false" />
202-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
203
204        <receiver
204-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
205            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
205-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
206            android:directBootAware="false"
206-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
207            android:enabled="true"
207-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
208            android:exported="false" />
208-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
209        <receiver
209-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
210            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
210-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
211            android:directBootAware="false"
211-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
212            android:enabled="false"
212-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
213            android:exported="false" >
213-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
214            <intent-filter>
214-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
215                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
215-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
215-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
216                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
216-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
216-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
217            </intent-filter>
218        </receiver>
219        <receiver
219-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
220            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
220-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
221            android:directBootAware="false"
221-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
222            android:enabled="false"
222-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
223            android:exported="false" >
223-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
224            <intent-filter>
224-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
225                <action android:name="android.intent.action.BATTERY_OKAY" />
225-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
225-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
226                <action android:name="android.intent.action.BATTERY_LOW" />
226-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
226-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
227            </intent-filter>
228        </receiver>
229        <receiver
229-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
230            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
230-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
231            android:directBootAware="false"
231-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
232            android:enabled="false"
232-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
233            android:exported="false" >
233-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
234            <intent-filter>
234-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
235                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
235-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
235-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
236                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
236-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
236-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
237            </intent-filter>
238        </receiver>
239        <receiver
239-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
240            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
240-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
241            android:directBootAware="false"
241-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
242            android:enabled="false"
242-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
243            android:exported="false" >
243-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
244            <intent-filter>
244-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
245                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
245-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
245-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
246            </intent-filter>
247        </receiver>
248        <receiver
248-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
249            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
249-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
250            android:directBootAware="false"
250-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
251            android:enabled="false"
251-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
252            android:exported="false" >
252-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
253            <intent-filter>
253-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
254                <action android:name="android.intent.action.BOOT_COMPLETED" />
254-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
254-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
255                <action android:name="android.intent.action.TIME_SET" />
255-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
255-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
256                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
256-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
256-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
257            </intent-filter>
258        </receiver>
259        <receiver
259-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
260            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
260-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
261            android:directBootAware="false"
261-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
262            android:enabled="@bool/enable_system_alarm_service_default"
262-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
263            android:exported="false" >
263-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
264            <intent-filter>
264-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
265                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
265-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
265-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
266            </intent-filter>
267        </receiver>
268        <receiver
268-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
269            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
269-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
270            android:directBootAware="false"
270-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
271            android:enabled="true"
271-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
272            android:exported="true"
272-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
273            android:permission="android.permission.DUMP" >
273-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
274            <intent-filter>
274-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
275                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
275-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
275-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
276            </intent-filter>
277        </receiver>
278
279        <service
279-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5bed4fbb8494397dfde4b98e6dcbc27\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
280            android:name="androidx.room.MultiInstanceInvalidationService"
280-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5bed4fbb8494397dfde4b98e6dcbc27\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
281            android:directBootAware="true"
281-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5bed4fbb8494397dfde4b98e6dcbc27\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
282            android:exported="false" />
282-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5bed4fbb8494397dfde4b98e6dcbc27\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
283
284        <receiver
284-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
285            android:name="androidx.profileinstaller.ProfileInstallReceiver"
285-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
286            android:directBootAware="false"
286-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
287            android:enabled="true"
287-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
288            android:exported="true"
288-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
289            android:permission="android.permission.DUMP" >
289-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
290            <intent-filter>
290-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
291                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
291-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
291-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
292            </intent-filter>
293            <intent-filter>
293-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
294                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
294-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
294-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
295            </intent-filter>
296            <intent-filter>
296-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
297                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
297-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
297-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
298            </intent-filter>
299            <intent-filter>
299-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
300                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
300-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
300-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
301            </intent-filter>
302        </receiver>
303
304        <service
304-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
305            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
305-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
306            android:exported="false" >
306-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
307            <meta-data
307-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
308                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
308-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
309                android:value="cct" />
309-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
310        </service>
311        <service
311-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
312            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
312-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
313            android:exported="false"
313-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
314            android:permission="android.permission.BIND_JOB_SERVICE" >
314-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
315        </service>
316
317        <receiver
317-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
318            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
318-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
319            android:exported="false" />
319-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
320    </application>
321
322</manifest>
