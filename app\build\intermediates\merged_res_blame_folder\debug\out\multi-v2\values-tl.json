{"logs": [{"outputFile": "com.example.standardtemplate.app-mergeDebugResources-42:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\436b23367b1f7618e21fb32754334ccb\\transformed\\play-services-basement-18.1.0\\res\\values-tl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5626", "endColumns": "144", "endOffsets": "5766"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8ddbf4dfd0b92e33e56ede5751f3d034\\transformed\\play-services-base-18.0.1\\res\\values-tl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,468,602,707,861,993,1111,1220,1395,1498,1672,1806,1964,2139,2203,2265", "endColumns": "102,171,133,104,153,131,117,108,174,102,173,133,157,174,63,61,76", "endOffsets": "295,467,601,706,860,992,1110,1219,1394,1497,1671,1805,1963,2138,2202,2264,2341"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4567,4674,4850,4988,5097,5255,5391,5513,5771,5950,6057,6235,6373,6535,6714,6782,6848", "endColumns": "106,175,137,108,157,135,121,112,178,106,177,137,161,178,67,65,80", "endOffsets": "4669,4845,4983,5092,5250,5386,5508,5621,5945,6052,6230,6368,6530,6709,6777,6843,6924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8b4e8af462e33194cf40c35189226a44\\transformed\\material-1.9.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,280,364,444,530,627,717,822,958,1043,1108,1207,1275,1334,1423,1491,1558,1621,1696,1764,1818,1938,1996,2058,2112,2187,2329,2419,2504,2649,2733,2816,2912,2970,3021,3087,3161,3239,3330,3404,3483,3556,3628,3732,3805,3904,4004,4078,4153,4260,4312,4379,4470,4564,4626,4690,4753,4872,4974,5083,5186,5248,5303", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,83,79,85,96,89,104,135,84,64,98,67,58,88,67,66,62,74,67,53,119,57,61,53,74,141,89,84,144,83,82,95,57,50,65,73,77,90,73,78,72,71,103,72,98,99,73,74,106,51,66,90,93,61,63,62,118,101,108,102,61,54,84", "endOffsets": "275,359,439,525,622,712,817,953,1038,1103,1202,1270,1329,1418,1486,1553,1616,1691,1759,1813,1933,1991,2053,2107,2182,2324,2414,2499,2644,2728,2811,2907,2965,3016,3082,3156,3234,3325,3399,3478,3551,3623,3727,3800,3899,3999,4073,4148,4255,4307,4374,4465,4559,4621,4685,4748,4867,4969,5078,5181,5243,5298,5383"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3154,3234,3320,3417,4241,4346,4482,6929,6994,7093,7161,7220,7309,7377,7444,7507,7582,7650,7704,7824,7882,7944,7998,8073,8215,8305,8390,8535,8619,8702,8798,8856,8907,8973,9047,9125,9216,9290,9369,9442,9514,9618,9691,9790,9890,9964,10039,10146,10198,10265,10356,10450,10512,10576,10639,10758,10860,10969,11072,11134,11189", "endLines": "5,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119", "endColumns": "12,83,79,85,96,89,104,135,84,64,98,67,58,88,67,66,62,74,67,53,119,57,61,53,74,141,89,84,144,83,82,95,57,50,65,73,77,90,73,78,72,71,103,72,98,99,73,74,106,51,66,90,93,61,63,62,118,101,108,102,61,54,84", "endOffsets": "325,3149,3229,3315,3412,3502,4341,4477,4562,6989,7088,7156,7215,7304,7372,7439,7502,7577,7645,7699,7819,7877,7939,7993,8068,8210,8300,8385,8530,8614,8697,8793,8851,8902,8968,9042,9120,9211,9285,9364,9437,9509,9613,9686,9785,9885,9959,10034,10141,10193,10260,10351,10445,10507,10571,10634,10753,10855,10964,11067,11129,11184,11269"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e9aab70fa351237a81d00b2d6a85dd8e\\transformed\\core-1.10.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "38,39,40,41,42,43,44,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3507,3604,3706,3807,3904,4011,4119,11359", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "3599,3701,3802,3899,4006,4114,4236,11455"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\21e183407c04a2262ad4621802469c0c\\transformed\\appcompat-1.6.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "330,441,549,662,750,856,971,1051,1128,1219,1312,1407,1501,1601,1694,1789,1883,1974,2065,2149,2258,2368,2469,2579,2697,2805,2968,11274", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "436,544,657,745,851,966,1046,1123,1214,1307,1402,1496,1596,1689,1784,1878,1969,2060,2144,2253,2363,2464,2574,2692,2800,2963,3065,11354"}}]}]}