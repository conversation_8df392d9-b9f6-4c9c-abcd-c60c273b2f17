-- Merging decision tree log ---
manifest
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:2:1-80:12
INJECTED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:2:1-80:12
INJECTED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:2:1-80:12
INJECTED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:2:1-80:12
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b4e8af462e33194cf40c35189226a44\transformed\material-1.9.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ac015394818289cc056b421c06b91d\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab24daf35747d03209fd7212788c9e68\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21e183407c04a2262ad4621802469c0c\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da04ef619528daa1d8bb43240abf60d4\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73099662db7bf98f4262269fb8a030e7\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:17:1-61:12
MERGED from [com.google.firebase:firebase-analytics:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c431bc4c8daa2784e98e95690b71678\transformed\firebase-analytics-21.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:17:1-37:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c378ae0930cf7138f36f216b6edbfed5\transformed\play-services-measurement-sdk-21.3.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ddbf4dfd0b92e33e56ede5751f3d034\transformed\play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0a0ca7567bbb385efc213a3af8cef9\transformed\play-services-measurement-impl-21.3.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b398e214105abf3579d296cc935ac29\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\742163307c058c3fdb407192cf4d9349\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20a9b7da3d732923c99477db9d1857f3\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4dffbedd76b50d763a326c21f9b597a4\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b17c4ad5726bcee640ce67fecfa959aa\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:15:1-33:12
MERGED from [com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\378eae791bdb3069d99d14a82ce883c7\transformed\firebase-installations-17.1.3\AndroidManifest.xml:2:1-23:12
MERGED from [com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6a6ecc94268920426818703c113b418\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e61bc86e5203df332c28c209fb1b9b0c\transformed\play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de8823648589ed472897676f20286455\transformed\play-services-measurement-base-21.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\195a385735012d0394b57304c6a14693\transformed\firebase-installations-interop-17.1.0\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a12582ee79f2b02409e767dbad1877\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\436b23367b1f7618e21fb32754334ccb\transformed\play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30f0018fbcbc1233facba35dbcefa946\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\159b8d9f5694c85ad4e7b68325f3c0d9\transformed\activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0c5935144b6b2d3cf9265635a0ba28e\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35f1c6224769944de111539ab4bfbf5a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cb6f6c13d90d9a02baba802ba1cd63\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18efbb5f63bc94fcfa414a87e3a065a8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66ce3c46535ce207f894f0ce27bed42a\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53f456924ecf195ed7c6abe536891a0e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2e6ac9ad96da81ed9571683ee0387f4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7add0eeee191a2631d6200ffb6df7f5e\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f56dc28345bd9bc741d37059c0ae643\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa845f3c6045edbccedbbf70b69932ba\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\919eb969bfe207ac267d5aa035a56bd9\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c967e6935ddea2d32a85e4bd50b38c3\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\94c48e52c0cf765edccbb022f0d9bab1\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1328923033da8ce6690f733d346fffff\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2eda3ccc913a5c4927379a64a26037aa\transformed\lifecycle-service-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61ad85b7c7ad0ad828891a4eb833da82\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ed964ef8543b811a25e416fface10a5\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab329edfa558998c5799f84b34eaaeeb\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e360eab0b6efd53b870491edfe323268\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da3bbca233be645ce4c3c6513aef9c3b\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fe2510b51a76553d0fe01e9c73a8ef8\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\028be9088e76290e676de3e8e181a3f5\transformed\core-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acfff3a9ad6c33e6b95bb6e437986053\transformed\room-ktx-2.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5bed4fbb8494397dfde4b98e6dcbc27\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c10939a53ca6c9838750a61f31a91ec\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\915003ed3f8a55248d61f1fe35f670f4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9f12821afd075b0266811c2f1b89750\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3df6cc11034f0f7e0731682ed2166121\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b69a9b35c0505a4f34b1c934fa71ab1\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0089fc8dbefc5147fc31733e85f95411\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25c0e4c8a3a4df37965f19dcb7178512\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22f1e9adf00db1bc8053284c5e8c749f\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d9980424064cbe97d92aa3780af1a1\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-components:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acbbf0dbf307d84d76046e626747eff5\transformed\firebase-components-17.1.0\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dec350fec9485f03e015bf53bcb128d3\transformed\transport-api-3.0.0\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5fb7abca3166b2aec1f87ad64e844991\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d4bfe6fe11cd2c4a8eb8da2b922a74a\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0729944f40d4642f73c513ac157afaad\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32c394f60e8bc2b11ce8360e8df7cfbd\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4820380da6bd253ea42439529d34bed3\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b05c25b1a1375ac58fd7b709dfb557\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7b1b79dfcdbcbc3025946c780c9e3a6\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0a0ca7567bbb385efc213a3af8cef9\transformed\play-services-measurement-impl-21.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0a0ca7567bbb385efc213a3af8cef9\transformed\play-services-measurement-impl-21.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4dffbedd76b50d763a326c21f9b597a4\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4dffbedd76b50d763a326c21f9b597a4\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\378eae791bdb3069d99d14a82ce883c7\transformed\firebase-installations-17.1.3\AndroidManifest.xml:10:5-67
MERGED from [com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\378eae791bdb3069d99d14a82ce883c7\transformed\firebase-installations-17.1.3\AndroidManifest.xml:10:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e61bc86e5203df332c28c209fb1b9b0c\transformed\play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e61bc86e5203df332c28c209fb1b9b0c\transformed\play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:6:5-79
MERGED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0a0ca7567bbb385efc213a3af8cef9\transformed\play-services-measurement-impl-21.3.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0a0ca7567bbb385efc213a3af8cef9\transformed\play-services-measurement-impl-21.3.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4dffbedd76b50d763a326c21f9b597a4\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4dffbedd76b50d763a326c21f9b597a4\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\378eae791bdb3069d99d14a82ce883c7\transformed\firebase-installations-17.1.3\AndroidManifest.xml:9:5-79
MERGED from [com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\378eae791bdb3069d99d14a82ce883c7\transformed\firebase-installations-17.1.3\AndroidManifest.xml:9:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e61bc86e5203df332c28c209fb1b9b0c\transformed\play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e61bc86e5203df332c28c209fb1b9b0c\transformed\play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:7:5-76
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:7:22-73
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:9:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:9:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_DATA_SYNC
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:10:5-87
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:10:22-84
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:11:5-77
MERGED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:25:5-77
MERGED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:25:5-77
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:11:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_LOCATION
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:12:5-86
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:12:22-83
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:13:5-79
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:13:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:14:5-81
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:14:22-78
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:16:5-95
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:16:22-92
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:17:5-68
MERGED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:26:5-68
MERGED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:26:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0a0ca7567bbb385efc213a3af8cef9\transformed\play-services-measurement-impl-21.3.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0a0ca7567bbb385efc213a3af8cef9\transformed\play-services-measurement-impl-21.3.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4dffbedd76b50d763a326c21f9b597a4\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4dffbedd76b50d763a326c21f9b597a4\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e61bc86e5203df332c28c209fb1b9b0c\transformed\play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e61bc86e5203df332c28c209fb1b9b0c\transformed\play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:17:22-65
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:18:5-81
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:18:22-78
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:19:5-92
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:19:22-89
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:21:5-80
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:21:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:22:5-81
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:22:22-78
application
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:24:5-78:19
INJECTED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:24:5-78:19
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b4e8af462e33194cf40c35189226a44\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b4e8af462e33194cf40c35189226a44\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ac015394818289cc056b421c06b91d\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ac015394818289cc056b421c06b91d\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:30:5-59:19
MERGED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:30:5-59:19
MERGED from [com.google.firebase:firebase-analytics:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c431bc4c8daa2784e98e95690b71678\transformed\firebase-analytics-21.3.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c431bc4c8daa2784e98e95690b71678\transformed\firebase-analytics-21.3.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c378ae0930cf7138f36f216b6edbfed5\transformed\play-services-measurement-sdk-21.3.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c378ae0930cf7138f36f216b6edbfed5\transformed\play-services-measurement-sdk-21.3.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ddbf4dfd0b92e33e56ede5751f3d034\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ddbf4dfd0b92e33e56ede5751f3d034\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0a0ca7567bbb385efc213a3af8cef9\transformed\play-services-measurement-impl-21.3.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0a0ca7567bbb385efc213a3af8cef9\transformed\play-services-measurement-impl-21.3.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b398e214105abf3579d296cc935ac29\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b398e214105abf3579d296cc935ac29\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\742163307c058c3fdb407192cf4d9349\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\742163307c058c3fdb407192cf4d9349\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20a9b7da3d732923c99477db9d1857f3\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20a9b7da3d732923c99477db9d1857f3\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b17c4ad5726bcee640ce67fecfa959aa\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b17c4ad5726bcee640ce67fecfa959aa\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\378eae791bdb3069d99d14a82ce883c7\transformed\firebase-installations-17.1.3\AndroidManifest.xml:13:5-21:19
MERGED from [com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\378eae791bdb3069d99d14a82ce883c7\transformed\firebase-installations-17.1.3\AndroidManifest.xml:13:5-21:19
MERGED from [com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:24:5-37:19
MERGED from [com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:24:5-37:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6a6ecc94268920426818703c113b418\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6a6ecc94268920426818703c113b418\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de8823648589ed472897676f20286455\transformed\play-services-measurement-base-21.3.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de8823648589ed472897676f20286455\transformed\play-services-measurement-base-21.3.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a12582ee79f2b02409e767dbad1877\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a12582ee79f2b02409e767dbad1877\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\436b23367b1f7618e21fb32754334ccb\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\436b23367b1f7618e21fb32754334ccb\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ed964ef8543b811a25e416fface10a5\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ed964ef8543b811a25e416fface10a5\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5bed4fbb8494397dfde4b98e6dcbc27\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5bed4fbb8494397dfde4b98e6dcbc27\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3df6cc11034f0f7e0731682ed2166121\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3df6cc11034f0f7e0731682ed2166121\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25c0e4c8a3a4df37965f19dcb7178512\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25c0e4c8a3a4df37965f19dcb7178512\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
	android:extractNativeLibs
		INJECTED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:31:9-52
	android:roundIcon
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:32:9-54
	android:icon
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:29:9-43
	android:appComponentFactory
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:33:9-35
	android:label
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:30:9-41
	android:fullBackupContent
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:28:9-54
	tools:targetApi
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:36:9-29
	android:allowBackup
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:26:9-35
	android:theme
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:34:9-54
	android:dataExtractionRules
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:27:9-65
	android:usesCleartextTraffic
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:35:9-44
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:25:9-67
activity#com.example.standardtemplate.Activities.Register.RegisterActivity
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:37:9-39:40
	android:exported
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:39:13-37
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:38:13-65
activity#com.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:40:9-42:40
	android:exported
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:42:13-37
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:41:13-81
activity#com.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:43:9-45:40
	android:exported
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:45:13-37
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:44:13-84
activity#com.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:46:9-48:40
	android:exported
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:48:13-37
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:47:13-75
activity#com.example.standardtemplate.Activities.NewTickets.NewTicketListActivity
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:49:9-51:40
	android:exported
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:51:13-37
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:50:13-72
activity#com.example.standardtemplate.Activities.Login_Setting.LoginActivity
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:52:9-60:20
	android:exported
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:54:13-36
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:53:13-67
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:55:13-59:29
action#android.intent.action.MAIN
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:56:17-69
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:56:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:58:17-77
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:58:27-74
service#com.example.standardtemplate.Libraries.TicketMonitoring
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:62:9-65:56
	android:exported
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:64:13-37
	android:foregroundServiceType
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:65:13-53
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:63:13-55
service#com.example.standardtemplate.Libraries.NotificationService
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:66:9-69:61
	android:exported
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:68:13-37
	android:foregroundServiceType
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:69:13-58
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:67:13-58
service#com.example.standardtemplate.Libraries.FirebaseMessagingService
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:71:9-77:19
	android:exported
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:73:13-37
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:72:13-63
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:74:13-76:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:75:17-78
	android:name
		ADDED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:75:25-75
uses-sdk
INJECTED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml
INJECTED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b4e8af462e33194cf40c35189226a44\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b4e8af462e33194cf40c35189226a44\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ac015394818289cc056b421c06b91d\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ac015394818289cc056b421c06b91d\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab24daf35747d03209fd7212788c9e68\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab24daf35747d03209fd7212788c9e68\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21e183407c04a2262ad4621802469c0c\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21e183407c04a2262ad4621802469c0c\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da04ef619528daa1d8bb43240abf60d4\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da04ef619528daa1d8bb43240abf60d4\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73099662db7bf98f4262269fb8a030e7\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73099662db7bf98f4262269fb8a030e7\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-analytics:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c431bc4c8daa2784e98e95690b71678\transformed\firebase-analytics-21.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c431bc4c8daa2784e98e95690b71678\transformed\firebase-analytics-21.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c378ae0930cf7138f36f216b6edbfed5\transformed\play-services-measurement-sdk-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c378ae0930cf7138f36f216b6edbfed5\transformed\play-services-measurement-sdk-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ddbf4dfd0b92e33e56ede5751f3d034\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ddbf4dfd0b92e33e56ede5751f3d034\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0a0ca7567bbb385efc213a3af8cef9\transformed\play-services-measurement-impl-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0a0ca7567bbb385efc213a3af8cef9\transformed\play-services-measurement-impl-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b398e214105abf3579d296cc935ac29\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b398e214105abf3579d296cc935ac29\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\742163307c058c3fdb407192cf4d9349\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\742163307c058c3fdb407192cf4d9349\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20a9b7da3d732923c99477db9d1857f3\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20a9b7da3d732923c99477db9d1857f3\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4dffbedd76b50d763a326c21f9b597a4\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4dffbedd76b50d763a326c21f9b597a4\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b17c4ad5726bcee640ce67fecfa959aa\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b17c4ad5726bcee640ce67fecfa959aa\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\378eae791bdb3069d99d14a82ce883c7\transformed\firebase-installations-17.1.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\378eae791bdb3069d99d14a82ce883c7\transformed\firebase-installations-17.1.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6a6ecc94268920426818703c113b418\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6a6ecc94268920426818703c113b418\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e61bc86e5203df332c28c209fb1b9b0c\transformed\play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e61bc86e5203df332c28c209fb1b9b0c\transformed\play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de8823648589ed472897676f20286455\transformed\play-services-measurement-base-21.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de8823648589ed472897676f20286455\transformed\play-services-measurement-base-21.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\195a385735012d0394b57304c6a14693\transformed\firebase-installations-interop-17.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-installations-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\195a385735012d0394b57304c6a14693\transformed\firebase-installations-interop-17.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a12582ee79f2b02409e767dbad1877\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a12582ee79f2b02409e767dbad1877\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\436b23367b1f7618e21fb32754334ccb\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\436b23367b1f7618e21fb32754334ccb\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30f0018fbcbc1233facba35dbcefa946\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30f0018fbcbc1233facba35dbcefa946\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\159b8d9f5694c85ad4e7b68325f3c0d9\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\159b8d9f5694c85ad4e7b68325f3c0d9\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0c5935144b6b2d3cf9265635a0ba28e\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0c5935144b6b2d3cf9265635a0ba28e\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35f1c6224769944de111539ab4bfbf5a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35f1c6224769944de111539ab4bfbf5a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cb6f6c13d90d9a02baba802ba1cd63\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cb6f6c13d90d9a02baba802ba1cd63\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18efbb5f63bc94fcfa414a87e3a065a8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18efbb5f63bc94fcfa414a87e3a065a8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66ce3c46535ce207f894f0ce27bed42a\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66ce3c46535ce207f894f0ce27bed42a\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53f456924ecf195ed7c6abe536891a0e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53f456924ecf195ed7c6abe536891a0e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2e6ac9ad96da81ed9571683ee0387f4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2e6ac9ad96da81ed9571683ee0387f4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7add0eeee191a2631d6200ffb6df7f5e\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7add0eeee191a2631d6200ffb6df7f5e\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f56dc28345bd9bc741d37059c0ae643\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f56dc28345bd9bc741d37059c0ae643\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa845f3c6045edbccedbbf70b69932ba\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa845f3c6045edbccedbbf70b69932ba\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\919eb969bfe207ac267d5aa035a56bd9\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\919eb969bfe207ac267d5aa035a56bd9\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c967e6935ddea2d32a85e4bd50b38c3\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c967e6935ddea2d32a85e4bd50b38c3\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\94c48e52c0cf765edccbb022f0d9bab1\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\94c48e52c0cf765edccbb022f0d9bab1\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1328923033da8ce6690f733d346fffff\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1328923033da8ce6690f733d346fffff\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2eda3ccc913a5c4927379a64a26037aa\transformed\lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2eda3ccc913a5c4927379a64a26037aa\transformed\lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61ad85b7c7ad0ad828891a4eb833da82\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61ad85b7c7ad0ad828891a4eb833da82\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ed964ef8543b811a25e416fface10a5\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ed964ef8543b811a25e416fface10a5\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab329edfa558998c5799f84b34eaaeeb\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab329edfa558998c5799f84b34eaaeeb\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e360eab0b6efd53b870491edfe323268\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e360eab0b6efd53b870491edfe323268\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da3bbca233be645ce4c3c6513aef9c3b\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da3bbca233be645ce4c3c6513aef9c3b\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fe2510b51a76553d0fe01e9c73a8ef8\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fe2510b51a76553d0fe01e9c73a8ef8\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\028be9088e76290e676de3e8e181a3f5\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\028be9088e76290e676de3e8e181a3f5\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acfff3a9ad6c33e6b95bb6e437986053\transformed\room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acfff3a9ad6c33e6b95bb6e437986053\transformed\room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5bed4fbb8494397dfde4b98e6dcbc27\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5bed4fbb8494397dfde4b98e6dcbc27\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c10939a53ca6c9838750a61f31a91ec\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c10939a53ca6c9838750a61f31a91ec\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\915003ed3f8a55248d61f1fe35f670f4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\915003ed3f8a55248d61f1fe35f670f4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9f12821afd075b0266811c2f1b89750\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9f12821afd075b0266811c2f1b89750\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3df6cc11034f0f7e0731682ed2166121\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3df6cc11034f0f7e0731682ed2166121\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b69a9b35c0505a4f34b1c934fa71ab1\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b69a9b35c0505a4f34b1c934fa71ab1\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0089fc8dbefc5147fc31733e85f95411\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0089fc8dbefc5147fc31733e85f95411\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25c0e4c8a3a4df37965f19dcb7178512\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25c0e4c8a3a4df37965f19dcb7178512\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22f1e9adf00db1bc8053284c5e8c749f\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22f1e9adf00db1bc8053284c5e8c749f\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d9980424064cbe97d92aa3780af1a1\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d9980424064cbe97d92aa3780af1a1\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acbbf0dbf307d84d76046e626747eff5\transformed\firebase-components-17.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-components:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acbbf0dbf307d84d76046e626747eff5\transformed\firebase-components-17.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dec350fec9485f03e015bf53bcb128d3\transformed\transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dec350fec9485f03e015bf53bcb128d3\transformed\transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5fb7abca3166b2aec1f87ad64e844991\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5fb7abca3166b2aec1f87ad64e844991\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d4bfe6fe11cd2c4a8eb8da2b922a74a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d4bfe6fe11cd2c4a8eb8da2b922a74a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0729944f40d4642f73c513ac157afaad\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0729944f40d4642f73c513ac157afaad\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32c394f60e8bc2b11ce8360e8df7cfbd\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32c394f60e8bc2b11ce8360e8df7cfbd\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4820380da6bd253ea42439529d34bed3\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4820380da6bd253ea42439529d34bed3\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b05c25b1a1375ac58fd7b709dfb557\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b05c25b1a1375ac58fd7b709dfb557\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7b1b79dfcdbcbc3025946c780c9e3a6\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7b1b79dfcdbcbc3025946c780c9e3a6\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4dffbedd76b50d763a326c21f9b597a4\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4dffbedd76b50d763a326c21f9b597a4\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:28:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:31:9-38:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:33:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:34:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:32:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:35:13-37:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:36:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:36:25-78
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:44:9-51:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:47:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:46:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:45:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:52:9-58:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b17c4ad5726bcee640ce67fecfa959aa\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b17c4ad5726bcee640ce67fecfa959aa\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
MERGED from [com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\378eae791bdb3069d99d14a82ce883c7\transformed\firebase-installations-17.1.3\AndroidManifest.xml:14:9-20:19
MERGED from [com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\378eae791bdb3069d99d14a82ce883c7\transformed\firebase-installations-17.1.3\AndroidManifest.xml:14:9-20:19
MERGED from [com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:32:9-36:35
MERGED from [com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:32:9-36:35
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:54:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:36:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:34:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:53:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:55:13-57:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:57:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5063742066eba39b894ae6aa305e62d8\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:56:17-119
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0a0ca7567bbb385efc213a3af8cef9\transformed\play-services-measurement-impl-21.3.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0a0ca7567bbb385efc213a3af8cef9\transformed\play-services-measurement-impl-21.3.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8acb730a20432d39d9b2db30b9b15964\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:40:13-87
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0a0ca7567bbb385efc213a3af8cef9\transformed\play-services-measurement-impl-21.3.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0a0ca7567bbb385efc213a3af8cef9\transformed\play-services-measurement-impl-21.3.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6a6ecc94268920426818703c113b418\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6a6ecc94268920426818703c113b418\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e61bc86e5203df332c28c209fb1b9b0c\transformed\play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e61bc86e5203df332c28c209fb1b9b0c\transformed\play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:25:22-76
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:31:13-33:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:33:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42845ff4cac9f6654c9b697bad4780ce\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:32:17-139
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ddbf4dfd0b92e33e56ede5751f3d034\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ddbf4dfd0b92e33e56ede5751f3d034\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ddbf4dfd0b92e33e56ede5751f3d034\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ddbf4dfd0b92e33e56ede5751f3d034\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b17c4ad5726bcee640ce67fecfa959aa\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b17c4ad5726bcee640ce67fecfa959aa\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b17c4ad5726bcee640ce67fecfa959aa\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\378eae791bdb3069d99d14a82ce883c7\transformed\firebase-installations-17.1.3\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\378eae791bdb3069d99d14a82ce883c7\transformed\firebase-installations-17.1.3\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\378eae791bdb3069d99d14a82ce883c7\transformed\firebase-installations-17.1.3\AndroidManifest.xml:18:17-127
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:25:9-30:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:27:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:29:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:28:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:30:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f26dfa49d6d08cb25f67dcba44549c\transformed\firebase-common-20.3.2\AndroidManifest.xml:26:13-77
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\436b23367b1f7618e21fb32754334ccb\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\436b23367b1f7618e21fb32754334ccb\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\436b23367b1f7618e21fb32754334ccb\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ed964ef8543b811a25e416fface10a5\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ed964ef8543b811a25e416fface10a5\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3df6cc11034f0f7e0731682ed2166121\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3df6cc11034f0f7e0731682ed2166121\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09300de1ea027254361534679fff8449\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b2bab00e9ded2b086e53f4ef829f5c6\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
permission#com.example.standardtemplate.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.standardtemplate.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aab70fa351237a81d00b2d6a85dd8e\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ed964ef8543b811a25e416fface10a5\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ed964ef8543b811a25e416fface10a5\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ed964ef8543b811a25e416fface10a5\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5bed4fbb8494397dfde4b98e6dcbc27\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5bed4fbb8494397dfde4b98e6dcbc27\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5bed4fbb8494397dfde4b98e6dcbc27\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5bed4fbb8494397dfde4b98e6dcbc27\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5bed4fbb8494397dfde4b98e6dcbc27\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3dac1c9f262a3b5e8d1e617bf18c440\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b645c4348892a8d116411aa320c2785\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8709284f5b50f363560fd1d4dc23473\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
