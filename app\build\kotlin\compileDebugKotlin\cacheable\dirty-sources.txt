D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Activities\NewTickets\Dialog\CreateDialog.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Activities\NewTickets\Interface\CreateDialogListener.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Models\TicketDone.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Activities\NewTickets\NewTicketListActivity.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Activities\Register\RegisterActivity.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Models\LoginResponse.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Models\AcceptedTicketDto.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Activities\AcceptedTicket\Adapter\AcceptedTicketAdapter.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Libraries\Notificationservice.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Libraries\TicketMonitoring.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Models\NewTicketInfo.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Models\LoginInfo.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Models\NewTicketListResponse.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Activities\NewTickets\NewTicketDetailsActivity.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Models\RegisterInfo.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Models\UserDetails.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Libraries\ApiClient.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Libraries\LanguageSetting\BaseActivity.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Activities\Login_Setting\LoginActivity.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Models\FcmTokenRequest.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Activities\NewTickets\Adapter\NewTicketAdapter.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Libraries\Managers\UserInfoManager.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Activities\AcceptedTicket\AcceptedTicketDetailsActivity.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Activities\AcceptedTicket\AcceptedTicketListActivity.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Activities\Login_Setting\ReturnResult.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Dialogs\MessageDialog.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Dialogs\ConfirmationDialog.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Models\RegisterResponse.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Libraries\StandardFunction\SharedPref.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Libraries\LanguageSetting\StandardTemplate.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Activities\AcceptedTicket\Adapter\AcceptedTicketDto.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Activities\Login_Setting\Dialog\SettingDialog.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Libraries\NotificationHelper.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Libraries\FirebaseMessagingService.kt
D:\MobileProject\standardtemplate-kotlin\app\src\main\java\com\example\standardtemplate\Libraries\ApiInterface.kt