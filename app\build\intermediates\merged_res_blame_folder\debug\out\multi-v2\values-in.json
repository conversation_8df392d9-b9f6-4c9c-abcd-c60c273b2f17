{"logs": [{"outputFile": "com.example.standardtemplate.app-mergeDebugResources-42:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\436b23367b1f7618e21fb32754334ccb\\transformed\\play-services-basement-18.1.0\\res\\values-in\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5473", "endColumns": "131", "endOffsets": "5600"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e9aab70fa351237a81d00b2d6a85dd8e\\transformed\\core-1.10.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "38,39,40,41,42,43,44,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3432,3527,3629,3726,3823,3929,4047,10903", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "3522,3624,3721,3818,3924,4042,4157,10999"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8b4e8af462e33194cf40c35189226a44\\transformed\\material-1.9.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,348,424,503,593,678,784,900,983,1048,1142,1207,1266,1353,1415,1477,1537,1603,1665,1719,1831,1888,1949,2003,2075,2201,2287,2371,2510,2591,2672,2762,2815,2867,2933,3005,3089,3172,3247,3323,3396,3471,3556,3631,3723,3817,3891,3964,4058,4110,4179,4264,4351,4413,4477,4540,4643,4743,4838,4940,4997,5053", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,78,75,78,89,84,105,115,82,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,83,138,80,80,89,52,51,65,71,83,82,74,75,72,74,84,74,91,93,73,72,93,51,68,84,86,61,63,62,102,99,94,101,56,55,79", "endOffsets": "264,343,419,498,588,673,779,895,978,1043,1137,1202,1261,1348,1410,1472,1532,1598,1660,1714,1826,1883,1944,1998,2070,2196,2282,2366,2505,2586,2667,2757,2810,2862,2928,3000,3084,3167,3242,3318,3391,3466,3551,3626,3718,3812,3886,3959,4053,4105,4174,4259,4346,4408,4472,4535,4638,4738,4833,4935,4992,5048,5128"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3023,3102,3178,3257,3347,4162,4268,4384,6668,6733,6827,6892,6951,7038,7100,7162,7222,7288,7350,7404,7516,7573,7634,7688,7760,7886,7972,8056,8195,8276,8357,8447,8500,8552,8618,8690,8774,8857,8932,9008,9081,9156,9241,9316,9408,9502,9576,9649,9743,9795,9864,9949,10036,10098,10162,10225,10328,10428,10523,10625,10682,10738", "endLines": "5,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119", "endColumns": "12,78,75,78,89,84,105,115,82,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,83,138,80,80,89,52,51,65,71,83,82,74,75,72,74,84,74,91,93,73,72,93,51,68,84,86,61,63,62,102,99,94,101,56,55,79", "endOffsets": "314,3097,3173,3252,3342,3427,4263,4379,4462,6728,6822,6887,6946,7033,7095,7157,7217,7283,7345,7399,7511,7568,7629,7683,7755,7881,7967,8051,8190,8271,8352,8442,8495,8547,8613,8685,8769,8852,8927,9003,9076,9151,9236,9311,9403,9497,9571,9644,9738,9790,9859,9944,10031,10093,10157,10220,10323,10423,10518,10620,10677,10733,10813"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8ddbf4dfd0b92e33e56ede5751f3d034\\transformed\\play-services-base-18.0.1\\res\\values-in\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,578,680,831,954,1065,1167,1329,1430,1590,1712,1863,2003,2063,2119", "endColumns": "102,159,121,101,150,122,110,101,161,100,159,121,150,139,59,55,74", "endOffsets": "295,455,577,679,830,953,1064,1166,1328,1429,1589,1711,1862,2002,2062,2118,2193"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4467,4574,4738,4864,4970,5125,5252,5367,5605,5771,5876,6040,6166,6321,6465,6529,6589", "endColumns": "106,163,125,105,154,126,114,105,165,104,163,125,154,143,63,59,78", "endOffsets": "4569,4733,4859,4965,5120,5247,5362,5468,5766,5871,6035,6161,6316,6460,6524,6584,6663"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\21e183407c04a2262ad4621802469c0c\\transformed\\appcompat-1.6.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,2889"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,434,538,643,730,834,950,1033,1111,1202,1295,1390,1484,1584,1677,1772,1866,1957,2048,2134,2237,2342,2443,2547,2656,2764,2924,10818", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "429,533,638,725,829,945,1028,1106,1197,1290,1385,1479,1579,1672,1767,1861,1952,2043,2129,2232,2337,2438,2542,2651,2759,2919,3018,10898"}}]}]}